<?php
/**
 * API المستخدمين
 * Users API
 */

require_once '../config/database.php';
require_once '../includes/auth.php';

// التحقق من تسجيل الدخول
requireLogin();
requirePermission('admin');

header('Content-Type: application/json; charset=utf-8');

$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

try {
    $db = getDB();
    
    switch ($method) {
        case 'GET':
            if ($action === 'get' && isset($_GET['id'])) {
                // جلب مستخدم واحد
                $userId = (int)$_GET['id'];
                $stmt = $db->prepare("
                    SELECT id, username, full_name, email, role, is_active, created_at 
                    FROM users 
                    WHERE id = ?
                ");
                $stmt->execute([$userId]);
                $user = $stmt->fetch();
                
                if ($user) {
                    sendJsonResponse($user);
                } else {
                    sendJsonResponse(['error' => 'المستخدم غير موجود'], 404);
                }
            } else {
                // جلب جميع المستخدمين
                $stmt = $db->query("
                    SELECT id, username, full_name, email, role, is_active, created_at 
                    FROM users 
                    ORDER BY created_at DESC
                ");
                $users = $stmt->fetchAll();
                sendJsonResponse($users);
            }
            break;
            
        case 'POST':
            $input = json_decode(file_get_contents('php://input'), true);
            
            if ($action === 'create') {
                // إنشاء مستخدم جديد
                $auth = new Auth();
                $result = $auth->createUser($input);
                sendJsonResponse($result, $result['success'] ? 201 : 400);
            }
            break;
            
        case 'PUT':
            $input = json_decode(file_get_contents('php://input'), true);
            $userId = (int)($_GET['id'] ?? 0);
            
            if ($userId > 0) {
                // تحديث المستخدم
                $auth = new Auth();
                $result = $auth->updateUser($userId, $input);
                sendJsonResponse($result);
            } else {
                sendJsonResponse(['error' => 'معرف المستخدم مطلوب'], 400);
            }
            break;
            
        case 'DELETE':
            $userId = (int)($_GET['id'] ?? 0);
            
            if ($userId > 0) {
                // حذف المستخدم
                $auth = new Auth();
                $result = $auth->deleteUser($userId);
                sendJsonResponse($result);
            } else {
                sendJsonResponse(['error' => 'معرف المستخدم مطلوب'], 400);
            }
            break;
            
        default:
            sendJsonResponse(['error' => 'طريقة غير مدعومة'], 405);
    }
    
} catch (Exception $e) {
    logError("خطأ في API المستخدمين: " . $e->getMessage());
    sendJsonResponse(['error' => 'خطأ في الخادم'], 500);
}
?>

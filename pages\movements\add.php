<?php
/**
 * صفحة إضافة حركة وظيفية جديدة
 * Add New Job Movement Page
 */

require_once '../../config/database.php';
require_once '../../includes/auth.php';

// التحقق من تسجيل الدخول
requireLogin();

$auth = new Auth();
$currentUser = $auth->getCurrentUser();

$message = '';
$messageType = 'info';

// معالجة إرسال النموذج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db = getDB();
        $db->beginTransaction();
        
        // البيانات الأساسية للحركة
        $movementTypeId = (int)$_POST['movement_type_id'];
        $movementTitle = sanitize($_POST['movement_title']);
        $movementDescription = sanitize($_POST['movement_description']);
        $movementDate = $_POST['movement_date'];
        $isGroupMovement = isset($_POST['is_group_movement']) ? 1 : 0;
        
        // التحقق من صحة البيانات
        $errors = [];
        
        if ($movementTypeId <= 0) {
            $errors[] = 'نوع الحركة مطلوب';
        }
        
        if (empty($movementTitle)) {
            $errors[] = 'عنوان الحركة مطلوب';
        }
        
        if (empty($movementDate)) {
            $errors[] = 'تاريخ الحركة مطلوب';
        }
        
        // التحقق من وجود موظفين محددين
        $employees = [];
        if (isset($_POST['employees']) && is_array($_POST['employees'])) {
            $employees = array_filter($_POST['employees'], function($emp) {
                return !empty($emp['employee_id']);
            });
        }
        
        if (empty($employees)) {
            $errors[] = 'يجب تحديد موظف واحد على الأقل';
        }
        
        if (empty($errors)) {
            // رفع المستند إذا كان موجود
            $documentFile = null;
            if (isset($_FILES['document_file']) && $_FILES['document_file']['error'] === UPLOAD_ERR_OK) {
                $uploadDir = '../../uploads/movements/';
                createDirectoryIfNotExists($uploadDir);
                
                $fileName = time() . '_' . $_FILES['document_file']['name'];
                $filePath = $uploadDir . $fileName;
                
                if (move_uploaded_file($_FILES['document_file']['tmp_name'], $filePath)) {
                    $documentFile = 'uploads/movements/' . $fileName;
                }
            }
            
            // إدراج الحركة الوظيفية
            $stmt = $db->prepare("
                INSERT INTO job_movements 
                (movement_type_id, movement_title, movement_description, movement_date, 
                 is_group_movement, document_file, created_by) 
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $movementTypeId, $movementTitle, $movementDescription, $movementDate,
                $isGroupMovement, $documentFile, $currentUser['id']
            ]);
            
            $movementId = $db->lastInsertId();
            
            // إدراج تفاصيل الحركة للموظفين
            $stmt = $db->prepare("
                INSERT INTO job_movement_details 
                (movement_id, employee_id, from_department_id, to_department_id, 
                 from_circle_id, to_circle_id, from_section_id, to_section_id,
                 from_job_title_id, to_job_title_id, from_grade, to_grade,
                 from_level, to_level, settlement_amount, external_entity, notes) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            foreach ($employees as $employee) {
                $stmt->execute([
                    $movementId,
                    (int)$employee['employee_id'],
                    !empty($employee['from_department_id']) ? (int)$employee['from_department_id'] : null,
                    !empty($employee['to_department_id']) ? (int)$employee['to_department_id'] : null,
                    !empty($employee['from_circle_id']) ? (int)$employee['from_circle_id'] : null,
                    !empty($employee['to_circle_id']) ? (int)$employee['to_circle_id'] : null,
                    !empty($employee['from_section_id']) ? (int)$employee['from_section_id'] : null,
                    !empty($employee['to_section_id']) ? (int)$employee['to_section_id'] : null,
                    !empty($employee['from_job_title_id']) ? (int)$employee['from_job_title_id'] : null,
                    !empty($employee['to_job_title_id']) ? (int)$employee['to_job_title_id'] : null,
                    !empty($employee['from_grade']) ? (int)$employee['from_grade'] : null,
                    !empty($employee['to_grade']) ? (int)$employee['to_grade'] : null,
                    !empty($employee['from_level']) ? (int)$employee['from_level'] : null,
                    !empty($employee['to_level']) ? (int)$employee['to_level'] : null,
                    !empty($employee['settlement_amount']) ? (float)$employee['settlement_amount'] : null,
                    sanitize($employee['external_entity'] ?? ''),
                    sanitize($employee['notes'] ?? '')
                ]);
            }
            
            $db->commit();
            
            $message = 'تم إضافة الحركة الوظيفية بنجاح';
            $messageType = 'success';
            
            // إعادة توجيه إلى صفحة عرض الحركة
            header("Location: view.php?id=$movementId&message=" . urlencode($message));
            exit;
            
        } else {
            $db->rollBack();
            $message = implode('<br>', $errors);
            $messageType = 'danger';
        }
        
    } catch (Exception $e) {
        $db->rollBack();
        logError("خطأ في إضافة الحركة الوظيفية: " . $e->getMessage());
        $message = 'حدث خطأ في إضافة الحركة الوظيفية';
        $messageType = 'danger';
    }
}

// جلب البيانات المرجعية
try {
    $db = getDB();
    
    // جلب أنواع الحركات الوظيفية
    $stmt = $db->query("SELECT id, type_name, is_group_action FROM job_movement_types WHERE is_active = 1 ORDER BY type_name");
    $movementTypes = $stmt->fetchAll();
    
    // جلب الإدارات
    $stmt = $db->query("SELECT id, dept_name FROM departments WHERE is_active = 1 ORDER BY dept_name");
    $departments = $stmt->fetchAll();
    
    // جلب المسميات الوظيفية
    $stmt = $db->query("SELECT id, title_name FROM job_titles WHERE is_active = 1 ORDER BY title_name");
    $jobTitles = $stmt->fetchAll();
    
} catch (Exception $e) {
    logError("خطأ في جلب البيانات المرجعية: " . $e->getMessage());
    $movementTypes = [];
    $departments = [];
    $jobTitles = [];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة حركة وظيفية جديدة - <?php echo SYSTEM_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="../../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <?php include '../../includes/navbar.php'; ?>
    
    <!-- Sidebar -->
    <?php include '../../includes/sidebar.php'; ?>
    
    <!-- Main Content -->
    <div class="main-content">
        <div class="container-fluid">
            <!-- Page Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="page-header">
                        <h1 class="page-title">
                            <i class="bi bi-arrow-left-right me-2"></i>
                            إضافة حركة وظيفية جديدة
                        </h1>
                        <p class="page-subtitle">
                            إنشاء حركة وظيفية فردية أو جماعية للموظفين
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- Messages -->
            <?php if (!empty($message)): ?>
                <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                    <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <!-- Movement Form -->
            <form method="POST" enctype="multipart/form-data" class="needs-validation" novalidate>
                <div class="row">
                    <!-- معلومات الحركة الأساسية -->
                    <div class="col-12 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-info-circle me-2"></i>
                                    معلومات الحركة الأساسية
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="movement_type_id" class="form-label">نوع الحركة *</label>
                                        <select class="form-select" id="movement_type_id" name="movement_type_id" required>
                                            <option value="">اختر نوع الحركة</option>
                                            <?php foreach ($movementTypes as $type): ?>
                                                <option value="<?php echo $type['id']; ?>" 
                                                        data-group="<?php echo $type['is_group_action']; ?>"
                                                        <?php echo (($_POST['movement_type_id'] ?? '') == $type['id']) ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($type['type_name']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <div class="invalid-feedback">يرجى اختيار نوع الحركة</div>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="movement_date" class="form-label">تاريخ الحركة *</label>
                                        <input type="date" class="form-control" id="movement_date" 
                                               name="movement_date" required 
                                               value="<?php echo htmlspecialchars($_POST['movement_date'] ?? date('Y-m-d')); ?>">
                                        <div class="invalid-feedback">يرجى إدخال تاريخ الحركة</div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="movement_title" class="form-label">عنوان الحركة *</label>
                                    <input type="text" class="form-control" id="movement_title" 
                                           name="movement_title" required 
                                           value="<?php echo htmlspecialchars($_POST['movement_title'] ?? ''); ?>">
                                    <div class="invalid-feedback">يرجى إدخال عنوان الحركة</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="movement_description" class="form-label">وصف الحركة</label>
                                    <textarea class="form-control" id="movement_description" 
                                              name="movement_description" rows="3"><?php echo htmlspecialchars($_POST['movement_description'] ?? ''); ?></textarea>
                                </div>
                                
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="is_group_movement" 
                                               name="is_group_movement" <?php echo isset($_POST['is_group_movement']) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="is_group_movement">
                                            حركة جماعية
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- تفاصيل الموظفين -->
                    <div class="col-12 mb-4">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-people me-2"></i>
                                    تفاصيل الموظفين
                                </h5>
                                <button type="button" class="btn btn-sm btn-primary" onclick="addEmployeeRow()">
                                    <i class="bi bi-plus me-1"></i>
                                    إضافة موظف
                                </button>
                            </div>
                            <div class="card-body">
                                <div id="employees-container">
                                    <!-- سيتم إضافة صفوف الموظفين هنا ديناميكياً -->
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- رفع المستند -->
                    <div class="col-12 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-file-earmark-arrow-up me-2"></i>
                                    رفع المستند
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="document_file" class="form-label">اختر الملف</label>
                                    <input type="file" class="form-control" id="document_file" name="document_file" 
                                           accept=".pdf,.jpg,.jpeg,.png,.doc,.docx,.xls,.xlsx">
                                    <div class="form-text">
                                        الأنواع المسموحة: PDF, JPG, JPEG, PNG, DOC, DOCX, XLS, XLSX<br>
                                        الحد الأقصى للحجم: 10 MB
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body">
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-save me-2"></i>
                                        حفظ الحركة
                                    </button>
                                    <a href="list.php" class="btn btn-secondary">
                                        <i class="bi bi-arrow-right me-2"></i>
                                        العودة إلى القائمة
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Employee Row Template -->
    <template id="employee-row-template">
        <div class="employee-row border rounded p-3 mb-3">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h6 class="mb-0">موظف #<span class="employee-number">1</span></h6>
                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeEmployeeRow(this)">
                    <i class="bi bi-trash"></i>
                </button>
            </div>
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label class="form-label">اسم الموظف *</label>
                    <select class="form-select employee-select" name="employees[0][employee_id]" required>
                        <option value="">اختر الموظف</option>
                    </select>
                </div>
                
                <!-- حقول ديناميكية حسب نوع الحركة -->
                <div class="movement-fields">
                    <!-- سيتم إضافة الحقول هنا حسب نوع الحركة -->
                </div>
            </div>
        </div>
    </template>
    
    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="../../assets/js/main.js"></script>
    
    <script>
        let employeeCounter = 0;
        
        // تهيئة الصفحة
        $(document).ready(function() {
            // إضافة صف موظف أول
            addEmployeeRow();
            
            // تحديث الحقول عند تغيير نوع الحركة
            $('#movement_type_id').on('change', function() {
                updateMovementFields();
            });
        });
        
        // إضافة صف موظف جديد
        function addEmployeeRow() {
            employeeCounter++;
            
            const template = document.getElementById('employee-row-template');
            const clone = template.content.cloneNode(true);
            
            // تحديث الأرقام والأسماء
            clone.querySelector('.employee-number').textContent = employeeCounter;
            clone.querySelector('.employee-select').name = `employees[${employeeCounter}][employee_id]`;
            
            // إضافة الصف إلى الحاوية
            document.getElementById('employees-container').appendChild(clone);
            
            // تهيئة Select2 للموظف الجديد
            initEmployeeSelect(employeeCounter);
            
            // تحديث حقول الحركة
            updateMovementFields();
        }
        
        // إزالة صف موظف
        function removeEmployeeRow(button) {
            if (document.querySelectorAll('.employee-row').length > 1) {
                button.closest('.employee-row').remove();
            } else {
                ALarchef.showMessage('يجب أن يكون هناك موظف واحد على الأقل', 'warning');
            }
        }
        
        // تهيئة Select2 للموظف
        function initEmployeeSelect(counter) {
            $(`select[name="employees[${counter}][employee_id]"]`).select2({
                theme: 'bootstrap-5',
                placeholder: 'ابحث عن الموظف...',
                allowClear: true,
                ajax: {
                    url: '../../api/search_employees.php',
                    dataType: 'json',
                    delay: 250,
                    data: function (params) {
                        return {
                            q: params.term,
                            page: params.page
                        };
                    },
                    processResults: function (data, params) {
                        params.page = params.page || 1;
                        
                        return {
                            results: data.items,
                            pagination: {
                                more: (params.page * 30) < data.total_count
                            }
                        };
                    },
                    cache: true
                }
            });
        }
        
        // تحديث حقول الحركة حسب النوع
        function updateMovementFields() {
            const movementType = $('#movement_type_id option:selected').text();
            const isGroup = $('#movement_type_id option:selected').data('group');
            
            // تحديث عنوان الحركة تلقائياً
            if ($('#movement_title').val() === '') {
                $('#movement_title').val(movementType);
            }
            
            // تحديث حالة الحركة الجماعية
            $('#is_group_movement').prop('checked', isGroup == 1);
            
            // تحديث حقول كل موظف
            $('.employee-row').each(function() {
                updateEmployeeFields(this, movementType);
            });
        }
        
        // تحديث حقول الموظف حسب نوع الحركة
        function updateEmployeeFields(employeeRow, movementType) {
            const fieldsContainer = employeeRow.querySelector('.movement-fields');
            let fieldsHtml = '';
            
            if (movementType.includes('نقل')) {
                fieldsHtml = `
                    <div class="col-md-3 mb-3">
                        <label class="form-label">من الإدارة</label>
                        <select class="form-select" name="employees[${employeeCounter}][from_department_id]">
                            <option value="">اختر الإدارة</option>
                            <?php foreach ($departments as $dept): ?>
                                <option value="<?php echo $dept['id']; ?>"><?php echo htmlspecialchars($dept['dept_name']); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label class="form-label">إلى الإدارة</label>
                        <select class="form-select" name="employees[${employeeCounter}][to_department_id]">
                            <option value="">اختر الإدارة</option>
                            <?php foreach ($departments as $dept): ?>
                                <option value="<?php echo $dept['id']; ?>"><?php echo htmlspecialchars($dept['dept_name']); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                `;
            } else if (movementType.includes('اعاره')) {
                fieldsHtml = `
                    <div class="col-md-3 mb-3">
                        <label class="form-label">الجهة المنقول منها</label>
                        <input type="text" class="form-control" name="employees[${employeeCounter}][external_entity]">
                    </div>
                    <div class="col-md-3 mb-3">
                        <label class="form-label">الجهة المنقول إليها</label>
                        <input type="text" class="form-control" name="employees[${employeeCounter}][external_entity]">
                    </div>
                `;
            } else if (movementType.includes('تسوية')) {
                fieldsHtml = `
                    <div class="col-md-2 mb-3">
                        <label class="form-label">الدرجة السابقة</label>
                        <input type="number" class="form-control" name="employees[${employeeCounter}][from_grade]" min="1" max="20">
                    </div>
                    <div class="col-md-2 mb-3">
                        <label class="form-label">الدرجة الحالية</label>
                        <input type="number" class="form-control" name="employees[${employeeCounter}][to_grade]" min="1" max="20">
                    </div>
                    <div class="col-md-2 mb-3">
                        <label class="form-label">مبلغ التسوية</label>
                        <input type="number" class="form-control" name="employees[${employeeCounter}][settlement_amount]" step="0.01">
                    </div>
                `;
            }
            
            // إضافة حقل الملاحظات دائماً
            fieldsHtml += `
                <div class="col-12 mb-3">
                    <label class="form-label">ملاحظات</label>
                    <textarea class="form-control" name="employees[${employeeCounter}][notes]" rows="2"></textarea>
                </div>
            `;
            
            fieldsContainer.innerHTML = fieldsHtml;
        }
    </script>
</body>
</html>

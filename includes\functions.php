<?php
/**
 * دوال مساعدة عامة
 * General Helper Functions
 */

// دالة لتحديد المسار الصحيح
function getCorrectPath($relativePath) {
    $paths = [
        __DIR__ . '/../' . $relativePath,
        '../' . $relativePath,
        $relativePath,
        './' . $relativePath
    ];
    
    foreach ($paths as $path) {
        if (file_exists($path)) {
            return $path;
        }
    }
    
    return $relativePath; // إرجاع المسار الأصلي إذا لم يوجد
}

// دالة لتحديد URL الصحيح
function getBaseUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $path = '/ALarchef2/';
    
    return $protocol . '://' . $host . $path;
}

// دالة لإعادة التوجيه الآمن
function safeRedirect($path) {
    $baseUrl = getBaseUrl();
    
    // إذا كان المسار يبدأ بـ / فهو مسار مطلق
    if (strpos($path, '/') === 0) {
        header('Location: ' . $baseUrl . ltrim($path, '/'));
    } else {
        header('Location: ' . $path);
    }
    exit;
}

// دالة للتحقق من وجود قاعدة البيانات
function checkDatabaseConnection() {
    try {
        $db = getDB();
        return true;
    } catch (Exception $e) {
        return false;
    }
}

// دالة لإنشاء رابط آمن
function createLink($path, $text, $class = '') {
    $baseUrl = getBaseUrl();
    $fullUrl = $baseUrl . ltrim($path, '/');
    $classAttr = $class ? ' class="' . htmlspecialchars($class) . '"' : '';
    
    return '<a href="' . htmlspecialchars($fullUrl) . '"' . $classAttr . '>' . htmlspecialchars($text) . '</a>';
}

// دالة لتحديد المسار النسبي للصفحة الحالية
function getCurrentPagePath() {
    $scriptName = $_SERVER['SCRIPT_NAME'];
    $basePath = '/ALarchef2/';
    
    if (strpos($scriptName, $basePath) === 0) {
        return substr($scriptName, strlen($basePath));
    }
    
    return $scriptName;
}

// دالة لتحديد ما إذا كنا في صفحة معينة
function isCurrentPage($pagePath) {
    $currentPath = getCurrentPagePath();
    return $currentPath === $pagePath || basename($currentPath) === basename($pagePath);
}

// دالة لإنشاء breadcrumb
function createBreadcrumb($items) {
    $html = '<nav aria-label="breadcrumb"><ol class="breadcrumb">';
    
    foreach ($items as $index => $item) {
        $isLast = $index === count($items) - 1;
        
        if ($isLast) {
            $html .= '<li class="breadcrumb-item active" aria-current="page">' . htmlspecialchars($item['text']) . '</li>';
        } else {
            $html .= '<li class="breadcrumb-item">';
            if (isset($item['url'])) {
                $html .= '<a href="' . htmlspecialchars($item['url']) . '">' . htmlspecialchars($item['text']) . '</a>';
            } else {
                $html .= htmlspecialchars($item['text']);
            }
            $html .= '</li>';
        }
    }
    
    $html .= '</ol></nav>';
    return $html;
}

// دالة لتنظيف وتأمين المدخلات
function cleanInput($input) {
    if (is_array($input)) {
        return array_map('cleanInput', $input);
    }
    
    return htmlspecialchars(strip_tags(trim($input)), ENT_QUOTES, 'UTF-8');
}

// دالة للتحقق من صحة البريد الإلكتروني
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

// دالة للتحقق من صحة رقم الهاتف
function isValidPhone($phone) {
    // إزالة المسافات والرموز
    $phone = preg_replace('/[^0-9+]/', '', $phone);
    
    // التحقق من الطول والتنسيق
    return preg_match('/^(\+967|00967|967)?[0-9]{8,9}$/', $phone);
}

// دالة لتنسيق رقم الهاتف
function formatPhone($phone) {
    $phone = preg_replace('/[^0-9]/', '', $phone);
    
    if (strlen($phone) === 9) {
        return '+967-' . substr($phone, 0, 1) . '-' . substr($phone, 1, 3) . '-' . substr($phone, 4);
    } elseif (strlen($phone) === 8) {
        return '+967-' . substr($phone, 0, 1) . '-' . substr($phone, 1, 3) . '-' . substr($phone, 4);
    }
    
    return $phone;
}

// دالة لإنشاء كلمة مرور عشوائية
function generateRandomPassword($length = 8) {
    $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    $password = '';
    
    for ($i = 0; $i < $length; $i++) {
        $password .= $characters[rand(0, strlen($characters) - 1)];
    }
    
    return $password;
}

// دالة للتحقق من قوة كلمة المرور
function checkPasswordStrength($password) {
    $score = 0;
    $feedback = [];
    
    // الطول
    if (strlen($password) >= 8) {
        $score += 2;
    } else {
        $feedback[] = 'يجب أن تكون كلمة المرور 8 أحرف على الأقل';
    }
    
    // الأحرف الكبيرة
    if (preg_match('/[A-Z]/', $password)) {
        $score += 1;
    } else {
        $feedback[] = 'يجب أن تحتوي على حرف كبير واحد على الأقل';
    }
    
    // الأحرف الصغيرة
    if (preg_match('/[a-z]/', $password)) {
        $score += 1;
    } else {
        $feedback[] = 'يجب أن تحتوي على حرف صغير واحد على الأقل';
    }
    
    // الأرقام
    if (preg_match('/[0-9]/', $password)) {
        $score += 1;
    } else {
        $feedback[] = 'يجب أن تحتوي على رقم واحد على الأقل';
    }
    
    // الرموز الخاصة
    if (preg_match('/[^A-Za-z0-9]/', $password)) {
        $score += 1;
    } else {
        $feedback[] = 'يُنصح بإضافة رمز خاص (!@#$%^&*)';
    }
    
    // تحديد القوة
    if ($score >= 5) {
        $strength = 'قوية جداً';
        $class = 'success';
    } elseif ($score >= 4) {
        $strength = 'قوية';
        $class = 'success';
    } elseif ($score >= 3) {
        $strength = 'متوسطة';
        $class = 'warning';
    } else {
        $strength = 'ضعيفة';
        $class = 'danger';
    }
    
    return [
        'score' => $score,
        'strength' => $strength,
        'class' => $class,
        'feedback' => $feedback
    ];
}

// دالة لتحويل الوقت إلى تنسيق "منذ"
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) {
        return 'منذ لحظات';
    } elseif ($time < 3600) {
        $minutes = floor($time / 60);
        return "منذ $minutes دقيقة";
    } elseif ($time < 86400) {
        $hours = floor($time / 3600);
        return "منذ $hours ساعة";
    } elseif ($time < 2592000) {
        $days = floor($time / 86400);
        return "منذ $days يوم";
    } elseif ($time < 31536000) {
        $months = floor($time / 2592000);
        return "منذ $months شهر";
    } else {
        $years = floor($time / 31536000);
        return "منذ $years سنة";
    }
}

// دالة لضغط الصور
function compressImage($source, $destination, $quality = 75) {
    $info = getimagesize($source);
    
    if ($info['mime'] == 'image/jpeg') {
        $image = imagecreatefromjpeg($source);
        imagejpeg($image, $destination, $quality);
    } elseif ($info['mime'] == 'image/png') {
        $image = imagecreatefrompng($source);
        imagepng($image, $destination, 9 - ($quality / 10));
    } elseif ($info['mime'] == 'image/gif') {
        $image = imagecreatefromgif($source);
        imagegif($image, $destination);
    }
    
    if (isset($image)) {
        imagedestroy($image);
        return true;
    }
    
    return false;
}

// دالة لإنشاء صورة مصغرة
function createThumbnail($source, $destination, $width = 150, $height = 150) {
    $info = getimagesize($source);
    $originalWidth = $info[0];
    $originalHeight = $info[1];
    
    // حساب النسبة
    $ratio = min($width / $originalWidth, $height / $originalHeight);
    $newWidth = $originalWidth * $ratio;
    $newHeight = $originalHeight * $ratio;
    
    // إنشاء الصورة الجديدة
    $thumbnail = imagecreatetruecolor($newWidth, $newHeight);
    
    if ($info['mime'] == 'image/jpeg') {
        $source_image = imagecreatefromjpeg($source);
    } elseif ($info['mime'] == 'image/png') {
        $source_image = imagecreatefrompng($source);
        imagealphablending($thumbnail, false);
        imagesavealpha($thumbnail, true);
    } elseif ($info['mime'] == 'image/gif') {
        $source_image = imagecreatefromgif($source);
    } else {
        return false;
    }
    
    imagecopyresampled($thumbnail, $source_image, 0, 0, 0, 0, $newWidth, $newHeight, $originalWidth, $originalHeight);
    
    // حفظ الصورة المصغرة
    if ($info['mime'] == 'image/jpeg') {
        imagejpeg($thumbnail, $destination, 85);
    } elseif ($info['mime'] == 'image/png') {
        imagepng($thumbnail, $destination);
    } elseif ($info['mime'] == 'image/gif') {
        imagegif($thumbnail, $destination);
    }
    
    imagedestroy($thumbnail);
    imagedestroy($source_image);
    
    return true;
}
?>

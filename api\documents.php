<?php
/**
 * API المستندات
 * Documents API
 */

require_once '../config/database.php';
require_once '../includes/auth.php';

// التحقق من تسجيل الدخول
requireLogin();

header('Content-Type: application/json; charset=utf-8');

$method = $_SERVER['REQUEST_METHOD'];
$documentId = (int)($_GET['id'] ?? 0);

try {
    $db = getDB();
    $currentUser = (new Auth())->getCurrentUser();
    
    switch ($method) {
        case 'GET':
            if ($documentId > 0) {
                // جلب مستند واحد
                $stmt = $db->prepare("
                    SELECT 
                        d.*,
                        ep.full_name as employee_name,
                        ep.employee_number,
                        dt.type_name as document_type,
                        u.full_name as uploaded_by_name
                    FROM documents d
                    JOIN employees_personal ep ON d.employee_id = ep.id
                    JOIN document_types dt ON d.document_type_id = dt.id
                    JOIN users u ON d.uploaded_by = u.id
                    WHERE d.id = ? AND d.is_active = 1
                ");
                $stmt->execute([$documentId]);
                $document = $stmt->fetch();
                
                if ($document) {
                    sendJsonResponse($document);
                } else {
                    sendJsonResponse(['error' => 'المستند غير موجود'], 404);
                }
            } else {
                // جلب جميع المستندات
                $stmt = $db->query("
                    SELECT 
                        d.id,
                        d.document_title,
                        d.file_name,
                        d.file_type,
                        d.file_size,
                        d.upload_date,
                        ep.full_name as employee_name,
                        dt.type_name as document_type
                    FROM documents d
                    JOIN employees_personal ep ON d.employee_id = ep.id
                    JOIN document_types dt ON d.document_type_id = dt.id
                    WHERE d.is_active = 1
                    ORDER BY d.upload_date DESC
                ");
                $documents = $stmt->fetchAll();
                sendJsonResponse($documents);
            }
            break;
            
        case 'POST':
            // إنشاء مستند جديد (يتم التعامل معه في صفحة الرفع)
            sendJsonResponse(['error' => 'استخدم صفحة الرفع لإضافة مستندات جديدة'], 400);
            break;
            
        case 'PUT':
            // تحديث المستند
            if ($documentId <= 0) {
                sendJsonResponse(['error' => 'معرف المستند مطلوب'], 400);
            }
            
            $input = json_decode(file_get_contents('php://input'), true);
            
            $updateFields = [];
            $params = [];
            
            if (isset($input['document_title'])) {
                $updateFields[] = "document_title = ?";
                $params[] = sanitize($input['document_title']);
            }
            
            if (isset($input['document_description'])) {
                $updateFields[] = "document_description = ?";
                $params[] = sanitize($input['document_description']);
            }
            
            if (isset($input['document_type_id'])) {
                $updateFields[] = "document_type_id = ?";
                $params[] = (int)$input['document_type_id'];
            }
            
            if (empty($updateFields)) {
                sendJsonResponse(['error' => 'لا توجد بيانات للتحديث'], 400);
            }
            
            $params[] = $documentId;
            
            $sql = "UPDATE documents SET " . implode(', ', $updateFields) . " WHERE id = ? AND is_active = 1";
            $stmt = $db->prepare($sql);
            $stmt->execute($params);
            
            if ($stmt->rowCount() > 0) {
                sendJsonResponse(['success' => true, 'message' => 'تم تحديث المستند بنجاح']);
            } else {
                sendJsonResponse(['error' => 'فشل في تحديث المستند'], 400);
            }
            break;
            
        case 'DELETE':
            // حذف المستند
            if ($documentId <= 0) {
                sendJsonResponse(['error' => 'معرف المستند مطلوب'], 400);
            }
            
            // التحقق من وجود المستند
            $stmt = $db->prepare("SELECT file_path FROM documents WHERE id = ? AND is_active = 1");
            $stmt->execute([$documentId]);
            $document = $stmt->fetch();
            
            if (!$document) {
                sendJsonResponse(['error' => 'المستند غير موجود'], 404);
            }
            
            // حذف المستند من قاعدة البيانات (soft delete)
            $stmt = $db->prepare("UPDATE documents SET is_active = 0 WHERE id = ?");
            $stmt->execute([$documentId]);
            
            // حذف الملف الفعلي (اختياري)
            $filePath = '../' . $document['file_path'];
            if (file_exists($filePath)) {
                unlink($filePath);
            }
            
            sendJsonResponse(['success' => true, 'message' => 'تم حذف المستند بنجاح']);
            break;
            
        default:
            sendJsonResponse(['error' => 'طريقة غير مدعومة'], 405);
    }
    
} catch (Exception $e) {
    logError("خطأ في API المستندات: " . $e->getMessage());
    sendJsonResponse(['error' => 'خطأ في الخادم'], 500);
}
?>

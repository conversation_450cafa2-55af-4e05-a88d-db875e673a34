/**
 * ملف الأنماط المخصص - نظام أرشفة مصنع أسمنت البرح
 * Custom Styles - ALarchef Archive System
 */

/* الخطوط والألوان الأساسية */
:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --info-color: #0dcaf0;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --border-radius: 10px;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --transition: all 0.3s ease;
}

/* إعدادات عامة */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f6fa;
    line-height: 1.6;
}

/* رأس الصفحة */
.page-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    margin-bottom: 2rem;
    box-shadow: var(--box-shadow);
}

.page-title {
    font-size: 2rem;
    font-weight: 600;
    margin: 0;
}

.page-subtitle {
    margin: 0.5rem 0 0 0;
    opacity: 0.9;
    font-size: 1.1rem;
}

/* بطاقات الإحصائيات */
.stat-card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.stat-card .card-body {
    padding: 1.5rem;
    display: flex;
    align-items: center;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin-left: 1rem;
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    color: var(--dark-color);
}

.stat-label {
    margin: 0;
    color: var(--secondary-color);
    font-weight: 500;
}

.stat-card-primary .stat-icon {
    background: linear-gradient(135deg, var(--primary-color), #4c63d2);
}

.stat-card-success .stat-icon {
    background: linear-gradient(135deg, var(--success-color), #20c997);
}

.stat-card-info .stat-icon {
    background: linear-gradient(135deg, var(--info-color), #17a2b8);
}

.stat-card-warning .stat-icon {
    background: linear-gradient(135deg, var(--warning-color), #fd7e14);
}

/* البطاقات العامة */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
    background-color: var(--light-color);
    border-bottom: 1px solid #dee2e6;
    border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
    padding: 1rem 1.5rem;
}

.card-title {
    font-weight: 600;
    color: var(--dark-color);
}

/* الأزرار */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
    border: none;
    padding: 0.5rem 1rem;
}

.btn:hover {
    transform: translateY(-2px);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), #4c63d2);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color), #20c997);
}

.btn-info {
    background: linear-gradient(135deg, var(--info-color), #17a2b8);
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color), #fd7e14);
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color), #e74c3c);
}

/* أزرار الإجراءات السريعة */
.quick-action-btn {
    padding: 1.5rem;
    text-align: center;
    font-weight: 600;
    border-radius: var(--border-radius);
    transition: var(--transition);
    text-decoration: none;
    display: block;
    height: 100px;
}

.quick-action-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.quick-action-btn i {
    font-size: 1.5rem;
    display: block;
}

/* النماذج */
.form-control, .form-select {
    border-radius: var(--border-radius);
    border: 2px solid #e9ecef;
    transition: var(--transition);
    padding: 0.75rem;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-floating > label {
    padding: 1rem 0.75rem;
}

/* الجداول */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    background: white;
}

.table thead th {
    background-color: var(--light-color);
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: var(--dark-color);
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
}

/* الشارات */
.badge {
    border-radius: 20px;
    padding: 0.5rem 0.75rem;
    font-weight: 500;
}

/* التنبيهات */
.alert {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.alert-primary {
    background: linear-gradient(135deg, rgba(13, 110, 253, 0.1), rgba(13, 110, 253, 0.05));
    color: var(--primary-color);
}

.alert-success {
    background: linear-gradient(135deg, rgba(25, 135, 84, 0.1), rgba(25, 135, 84, 0.05));
    color: var(--success-color);
}

.alert-info {
    background: linear-gradient(135deg, rgba(13, 202, 240, 0.1), rgba(13, 202, 240, 0.05));
    color: var(--info-color);
}

.alert-warning {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 193, 7, 0.05));
    color: #b45309;
}

.alert-danger {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1), rgba(220, 53, 69, 0.05));
    color: var(--danger-color);
}

/* النوافذ المنبثقة */
.modal-content {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

.modal-header {
    border-bottom: 1px solid #dee2e6;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.modal-footer {
    border-top: 1px solid #dee2e6;
    border-radius: 0 0 var(--border-radius) var(--border-radius);
}

/* شريط التقدم */
.progress {
    border-radius: 20px;
    height: 10px;
}

.progress-bar {
    border-radius: 20px;
}

/* قوائم المجموعات */
.list-group {
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.list-group-item {
    border: none;
    transition: var(--transition);
}

.list-group-item:hover {
    background-color: rgba(13, 110, 253, 0.05);
    transform: translateX(-5px);
}

.list-group-item:first-child {
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.list-group-item:last-child {
    border-radius: 0 0 var(--border-radius) var(--border-radius);
}

/* التنقل بين الصفحات */
.pagination {
    border-radius: var(--border-radius);
}

.page-link {
    border: none;
    color: var(--primary-color);
    transition: var(--transition);
}

.page-link:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* أيقونات الملفات */
.file-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.file-icon.pdf {
    color: #dc3545;
}

.file-icon.doc, .file-icon.docx {
    color: #0d6efd;
}

.file-icon.xls, .file-icon.xlsx {
    color: #198754;
}

.file-icon.jpg, .file-icon.jpeg, .file-icon.png {
    color: #fd7e14;
}

/* حالات الموظفين */
.employee-status {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
}

.employee-status.active {
    background-color: rgba(25, 135, 84, 0.1);
    color: var(--success-color);
}

.employee-status.retired {
    background-color: rgba(108, 117, 125, 0.1);
    color: var(--secondary-color);
}

.employee-status.contract {
    background-color: rgba(255, 193, 7, 0.1);
    color: #b45309;
}

/* تحسينات للطباعة */
@media print {
    .sidebar, .navbar, .btn, .pagination {
        display: none !important;
    }
    
    .main-content {
        margin: 0 !important;
        padding: 0 !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #dee2e6 !important;
    }
    
    .page-header {
        background: white !important;
        color: black !important;
    }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .page-header {
        padding: 1rem;
    }
    
    .page-title {
        font-size: 1.5rem;
    }
    
    .stat-card .card-body {
        padding: 1rem;
    }
    
    .stat-number {
        font-size: 1.5rem;
    }
    
    .quick-action-btn {
        height: auto;
        padding: 1rem;
    }
}

/* تحسينات للوضع المظلم */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1a1a1a;
        color: #e9ecef;
    }
    
    .card {
        background-color: #2d3748;
        color: #e9ecef;
    }
    
    .table {
        background-color: #2d3748;
        color: #e9ecef;
    }
    
    .form-control, .form-select {
        background-color: #2d3748;
        border-color: #4a5568;
        color: #e9ecef;
    }
}

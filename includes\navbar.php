<?php
/**
 * شريط التنقل العلوي
 * Top Navigation Bar
 */

if (!isset($currentUser)) {
    $auth = new Auth();
    $currentUser = $auth->getCurrentUser();
}
?>

<nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
    <div class="container-fluid">
        <!-- Toggle Sidebar Button -->
        <button class="btn btn-outline-light me-3" type="button" id="sidebarToggle">
            <i class="bi bi-list"></i>
        </button>
        
        <!-- Brand -->
        <a class="navbar-brand" href="<?php echo BASE_URL; ?>">
            <i class="bi bi-building me-2"></i>
            <?php echo SYSTEM_NAME; ?>
        </a>
        
        <!-- Search Form -->
        <form class="d-flex me-auto" style="max-width: 400px;">
            <div class="input-group">
                <input class="form-control" type="search" placeholder="البحث عن موظف أو مستند..." 
                       aria-label="Search" id="globalSearch">
                <button class="btn btn-outline-light" type="submit">
                    <i class="bi bi-search"></i>
                </button>
            </div>
        </form>
        
        <!-- User Menu -->
        <div class="navbar-nav">
            <div class="nav-item dropdown">
                <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" 
                   id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                    <div class="user-avatar me-2">
                        <i class="bi bi-person-circle"></i>
                    </div>
                    <div class="user-info d-none d-md-block">
                        <div class="user-name"><?php echo htmlspecialchars($currentUser['full_name']); ?></div>
                        <div class="user-role">
                            <?php 
                            $roleNames = [
                                'admin' => 'مدير النظام',
                                'archive_staff' => 'موظف الأرشيف',
                                'medical_staff' => 'موظف الرعاية الطبية',
                                'employee' => 'موظف'
                            ];
                            echo $roleNames[$currentUser['role']] ?? $currentUser['role'];
                            ?>
                        </div>
                    </div>
                </a>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li>
                        <a class="dropdown-item" href="pages/profile.php">
                            <i class="bi bi-person me-2"></i>
                            الملف الشخصي
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item" href="pages/settings.php">
                            <i class="bi bi-gear me-2"></i>
                            الإعدادات
                        </a>
                    </li>
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <a class="dropdown-item text-danger" href="logout.php">
                            <i class="bi bi-box-arrow-right me-2"></i>
                            تسجيل الخروج
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</nav>

<!-- Search Results Modal -->
<div class="modal fade" id="searchModal" tabindex="-1" aria-labelledby="searchModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="searchModalLabel">نتائج البحث</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="searchResults">
                    <!-- Search results will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.navbar {
    box-shadow: 0 2px 4px rgba(0,0,0,.1);
    z-index: 1030;
}

.user-avatar {
    font-size: 1.5rem;
}

.user-info {
    text-align: right;
    line-height: 1.2;
}

.user-name {
    font-weight: 600;
    font-size: 0.9rem;
}

.user-role {
    font-size: 0.75rem;
    opacity: 0.8;
}

.dropdown-menu {
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,.1);
    border-radius: 10px;
}

.dropdown-item {
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
    transform: translateX(-5px);
}

#globalSearch {
    border-radius: 20px 0 0 20px;
    border: 2px solid rgba(255,255,255,0.3);
    background: rgba(255,255,255,0.1);
    color: white;
}

#globalSearch::placeholder {
    color: rgba(255,255,255,0.7);
}

#globalSearch:focus {
    background: white;
    color: #333;
    border-color: white;
}

.btn-outline-light {
    border-radius: 0 20px 20px 0;
    border: 2px solid rgba(255,255,255,0.3);
}

#sidebarToggle {
    border-radius: 8px;
}
</style>

<script>
// البحث العام
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('globalSearch');
    const searchForm = searchInput.closest('form');
    
    searchForm.addEventListener('submit', function(e) {
        e.preventDefault();
        performGlobalSearch(searchInput.value);
    });
    
    // البحث أثناء الكتابة
    let searchTimeout;
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        if (this.value.length >= 2) {
            searchTimeout = setTimeout(() => {
                performGlobalSearch(this.value);
            }, 500);
        }
    });
});

function performGlobalSearch(query) {
    if (query.length < 2) return;
    
    fetch('api/search.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ query: query })
    })
    .then(response => response.json())
    .then(data => {
        displaySearchResults(data);
    })
    .catch(error => {
        console.error('خطأ في البحث:', error);
    });
}

function displaySearchResults(results) {
    const searchResults = document.getElementById('searchResults');
    const searchModal = new bootstrap.Modal(document.getElementById('searchModal'));
    
    if (results.employees && results.employees.length > 0) {
        let html = '<h6>الموظفين:</h6><div class="list-group mb-3">';
        results.employees.forEach(employee => {
            html += `
                <a href="pages/employees/view.php?id=${employee.id}" class="list-group-item list-group-item-action">
                    <div class="d-flex w-100 justify-content-between">
                        <h6 class="mb-1">${employee.full_name}</h6>
                        <small>${employee.employee_number}</small>
                    </div>
                    <p class="mb-1">${employee.department_name || ''}</p>
                </a>
            `;
        });
        html += '</div>';
        searchResults.innerHTML = html;
    } else {
        searchResults.innerHTML = '<p class="text-muted">لا توجد نتائج للبحث</p>';
    }
    
    searchModal.show();
}
</script>

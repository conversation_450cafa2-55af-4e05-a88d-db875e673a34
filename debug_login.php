<?php
/**
 * تشخيص مشاكل صفحة تسجيل الدخول
 * Debug Login Page Issues
 */

echo "<h1>تشخيص مشاكل صفحة تسجيل الدخول</h1>";

// 1. التحقق من وجود الملفات المطلوبة
echo "<h2>1. التحقق من وجود الملفات:</h2>";

$requiredFiles = [
    'config/database.php',
    'includes/auth.php',
    'login.php'
];

foreach ($requiredFiles as $file) {
    if (file_exists($file)) {
        echo "✅ موجود: $file<br>";
    } else {
        echo "❌ مفقود: $file<br>";
    }
}

// 2. التحقق من اتصال قاعدة البيانات
echo "<h2>2. اختبار اتصال قاعدة البيانات:</h2>";

try {
    require_once 'config/database.php';
    $db = getDB();
    echo "✅ تم الاتصال بقاعدة البيانات بنجاح<br>";
    
    // التحقق من وجود جدول المستخدمين
    $stmt = $db->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() > 0) {
        echo "✅ جدول المستخدمين موجود<br>";
        
        // عدد المستخدمين
        $stmt = $db->query("SELECT COUNT(*) as count FROM users");
        $userCount = $stmt->fetch()['count'];
        echo "✅ يوجد $userCount مستخدم<br>";
        
    } else {
        echo "❌ جدول المستخدمين غير موجود<br>";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "<br>";
}

// 3. التحقق من إعدادات PHP
echo "<h2>3. إعدادات PHP:</h2>";
echo "إصدار PHP: " . phpversion() . "<br>";
echo "display_errors: " . (ini_get('display_errors') ? 'مفعل' : 'معطل') . "<br>";
echo "error_reporting: " . error_reporting() . "<br>";

// تفعيل عرض الأخطاء
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// 4. اختبار تضمين ملف auth.php
echo "<h2>4. اختبار ملف المصادقة:</h2>";

try {
    require_once 'includes/auth.php';
    echo "✅ تم تحميل ملف المصادقة بنجاح<br>";
    
    // اختبار إنشاء كائن Auth
    $auth = new Auth();
    echo "✅ تم إنشاء كائن المصادقة بنجاح<br>";
    
} catch (Exception $e) {
    echo "❌ خطأ في ملف المصادقة: " . $e->getMessage() . "<br>";
    echo "تفاصيل الخطأ:<br>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

// 5. اختبار محتوى ملف login.php
echo "<h2>5. اختبار محتوى ملف login.php:</h2>";

if (file_exists('login.php')) {
    $loginContent = file_get_contents('login.php');
    
    // البحث عن أخطاء شائعة
    if (strpos($loginContent, '<?php') === false) {
        echo "❌ ملف login.php لا يحتوي على علامة PHP الافتتاحية<br>";
    } else {
        echo "✅ ملف login.php يحتوي على علامة PHP الافتتاحية<br>";
    }
    
    if (strpos($loginContent, 'require_once') !== false) {
        echo "✅ ملف login.php يحتوي على require_once<br>";
    } else {
        echo "⚠️ ملف login.php لا يحتوي على require_once<br>";
    }
    
    // عرض أول 10 أسطر من الملف
    $lines = explode("\n", $loginContent);
    echo "<h3>أول 10 أسطر من login.php:</h3>";
    echo "<pre>";
    for ($i = 0; $i < min(10, count($lines)); $i++) {
        echo ($i + 1) . ": " . htmlspecialchars($lines[$i]) . "\n";
    }
    echo "</pre>";
}

// 6. إنشاء ملف login بسيط للاختبار
echo "<h2>6. إنشاء ملف login بسيط للاختبار:</h2>";

$simpleLogin = '<?php
// ملف تسجيل دخول بسيط للاختبار
echo "<h1>اختبار صفحة تسجيل الدخول</h1>";

try {
    require_once "config/database.php";
    echo "✅ تم تحميل ملف قاعدة البيانات<br>";
    
    require_once "includes/auth.php";
    echo "✅ تم تحميل ملف المصادقة<br>";
    
    $auth = new Auth();
    echo "✅ تم إنشاء كائن المصادقة<br>";
    
    echo "<p>النظام يعمل بشكل صحيح!</p>";
    echo "<p><a href=\"login.php\">العودة لصفحة تسجيل الدخول الأصلية</a></p>";
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage();
    echo "<br>تفاصيل: <pre>" . $e->getTraceAsString() . "</pre>";
}
?>';

file_put_contents('test_login.php', $simpleLogin);
echo "✅ تم إنشاء ملف test_login.php<br>";
echo "<p><a href='test_login.php' target='_blank'>اختبار صفحة تسجيل الدخول البسيطة</a></p>";

// 7. التحقق من صلاحيات الملفات
echo "<h2>7. صلاحيات الملفات:</h2>";

$filesToCheck = ['config/database.php', 'includes/auth.php', 'login.php'];

foreach ($filesToCheck as $file) {
    if (file_exists($file)) {
        $perms = fileperms($file);
        echo "$file: " . substr(sprintf('%o', $perms), -4) . "<br>";
    }
}

echo "<br><h2>الخلاصة:</h2>";
echo "<p>إذا كان كل شيء يظهر ✅، فالمشكلة قد تكون في:</p>";
echo "<ul>";
echo "<li>إعدادات Apache</li>";
echo "<li>ملف .htaccess</li>";
echo "<li>مسارات الملفات</li>";
echo "<li>ذاكرة PHP أو وقت التنفيذ</li>";
echo "</ul>";
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص مشاكل تسجيل الدخول</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        h1, h2, h3 { color: #333; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
</body>
</html>

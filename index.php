<?php
/**
 * الصفحة الرئيسية - لوحة التحكم
 * Main Dashboard Page
 */

require_once 'config/database.php';
require_once 'includes/auth.php';

// التحقق من تسجيل الدخول
requireLogin();

$auth = new Auth();
$currentUser = $auth->getCurrentUser();

// جلب الإحصائيات
try {
    $db = getDB();
    
    // إحصائيات الموظفين
    $stmt = $db->query("SELECT COUNT(*) as total FROM employees_personal WHERE is_active = 1");
    $totalEmployees = $stmt->fetch()['total'];
    
    // إحصائيات المستندات
    $stmt = $db->query("SELECT COUNT(*) as total FROM documents WHERE is_active = 1");
    $totalDocuments = $stmt->fetch()['total'];
    
    // إحصائيات المراسلات
    $stmt = $db->query("SELECT COUNT(*) as total FROM correspondences WHERE is_active = 1");
    $totalCorrespondences = $stmt->fetch()['total'];
    
    // إحصائيات الزيارات الطبية
    $stmt = $db->query("SELECT COUNT(*) as total FROM medical_visits WHERE is_active = 1");
    $totalMedicalVisits = $stmt->fetch()['total'];
    
    // الموظفين المقاربين للتقاعد (سن 58+)
    $stmt = $db->query("
        SELECT COUNT(*) as total 
        FROM employees_personal 
        WHERE is_active = 1 
        AND TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) >= 58
    ");
    $nearRetirement = $stmt->fetch()['total'];
    
    // آخر المستندات المرفوعة
    $stmt = $db->query("
        SELECT d.*, ep.full_name, dt.type_name, u.full_name as uploaded_by_name
        FROM documents d
        JOIN employees_personal ep ON d.employee_id = ep.id
        JOIN document_types dt ON d.document_type_id = dt.id
        JOIN users u ON d.uploaded_by = u.id
        WHERE d.is_active = 1
        ORDER BY d.upload_date DESC
        LIMIT 5
    ");
    $recentDocuments = $stmt->fetchAll();
    
} catch (Exception $e) {
    logError("خطأ في جلب الإحصائيات: " . $e->getMessage());
    $totalEmployees = $totalDocuments = $totalCorrespondences = $totalMedicalVisits = $nearRetirement = 0;
    $recentDocuments = [];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - <?php echo SYSTEM_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>
    
    <!-- Sidebar -->
    <?php include 'includes/sidebar.php'; ?>
    
    <!-- Main Content -->
    <div class="main-content">
        <div class="container-fluid">
            <!-- Page Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="page-header">
                        <h1 class="page-title">
                            <i class="bi bi-speedometer2 me-2"></i>
                            لوحة التحكم
                        </h1>
                        <p class="page-subtitle">
                            مرحباً <?php echo htmlspecialchars($currentUser['full_name']); ?>، 
                            اليوم <?php echo formatArabicDate(date('Y-m-d')); ?>
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card stat-card stat-card-primary">
                        <div class="card-body">
                            <div class="stat-icon">
                                <i class="bi bi-people-fill"></i>
                            </div>
                            <div class="stat-content">
                                <h3 class="stat-number"><?php echo convertToArabicNumbers($totalEmployees); ?></h3>
                                <p class="stat-label">إجمالي الموظفين</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card stat-card stat-card-success">
                        <div class="card-body">
                            <div class="stat-icon">
                                <i class="bi bi-file-earmark-text-fill"></i>
                            </div>
                            <div class="stat-content">
                                <h3 class="stat-number"><?php echo convertToArabicNumbers($totalDocuments); ?></h3>
                                <p class="stat-label">المستندات المؤرشفة</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card stat-card stat-card-info">
                        <div class="card-body">
                            <div class="stat-icon">
                                <i class="bi bi-envelope-fill"></i>
                            </div>
                            <div class="stat-content">
                                <h3 class="stat-number"><?php echo convertToArabicNumbers($totalCorrespondences); ?></h3>
                                <p class="stat-label">المراسلات</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card stat-card stat-card-warning">
                        <div class="card-body">
                            <div class="stat-icon">
                                <i class="bi bi-heart-pulse-fill"></i>
                            </div>
                            <div class="stat-content">
                                <h3 class="stat-number"><?php echo convertToArabicNumbers($totalMedicalVisits); ?></h3>
                                <p class="stat-label">الزيارات الطبية</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-lightning-charge me-2"></i>
                                الإجراءات السريعة
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-3 col-md-6 mb-3">
                                    <a href="pages/employees/add.php" class="btn btn-outline-primary w-100 quick-action-btn">
                                        <i class="bi bi-person-plus-fill mb-2"></i>
                                        <br>إضافة موظف جديد
                                    </a>
                                </div>
                                <div class="col-lg-3 col-md-6 mb-3">
                                    <a href="pages/documents/upload.php" class="btn btn-outline-success w-100 quick-action-btn">
                                        <i class="bi bi-cloud-upload-fill mb-2"></i>
                                        <br>رفع مستند
                                    </a>
                                </div>
                                <div class="col-lg-3 col-md-6 mb-3">
                                    <a href="pages/correspondences/add.php" class="btn btn-outline-info w-100 quick-action-btn">
                                        <i class="bi bi-envelope-plus-fill mb-2"></i>
                                        <br>إضافة مراسلة
                                    </a>
                                </div>
                                <div class="col-lg-3 col-md-6 mb-3">
                                    <a href="pages/medical/add_visit.php" class="btn btn-outline-warning w-100 quick-action-btn">
                                        <i class="bi bi-hospital-fill mb-2"></i>
                                        <br>تسجيل زيارة طبية
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Recent Activity and Alerts -->
            <div class="row">
                <!-- Recent Documents -->
                <div class="col-lg-8 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-clock-history me-2"></i>
                                آخر المستندات المرفوعة
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($recentDocuments)): ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>اسم الموظف</th>
                                                <th>نوع المستند</th>
                                                <th>عنوان المستند</th>
                                                <th>تاريخ الرفع</th>
                                                <th>رفع بواسطة</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($recentDocuments as $doc): ?>
                                                <tr>
                                                    <td><?php echo htmlspecialchars($doc['full_name']); ?></td>
                                                    <td>
                                                        <span class="badge bg-primary">
                                                            <?php echo htmlspecialchars($doc['type_name']); ?>
                                                        </span>
                                                    </td>
                                                    <td><?php echo htmlspecialchars($doc['document_title']); ?></td>
                                                    <td><?php echo formatArabicDate($doc['upload_date']); ?></td>
                                                    <td><?php echo htmlspecialchars($doc['uploaded_by_name']); ?></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-4">
                                    <i class="bi bi-inbox display-4 text-muted"></i>
                                    <p class="text-muted mt-2">لا توجد مستندات مرفوعة حديثاً</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <!-- Alerts and Notifications -->
                <div class="col-lg-4 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-bell me-2"></i>
                                التنبيهات
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php if ($nearRetirement > 0): ?>
                                <div class="alert alert-warning">
                                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                    <strong><?php echo convertToArabicNumbers($nearRetirement); ?></strong>
                                    موظف مقارب للتقاعد
                                    <br>
                                    <small>يرجى مراجعة ملفاتهم</small>
                                </div>
                            <?php endif; ?>
                            
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle-fill me-2"></i>
                                <strong>نصيحة:</strong>
                                يمكنك استخدام البحث السريع للعثور على الموظفين والمستندات
                            </div>
                            
                            <div class="alert alert-success">
                                <i class="bi bi-shield-check me-2"></i>
                                النظام يعمل بشكل طبيعي
                                <br>
                                <small>آخر نسخة احتياطية: اليوم</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="assets/js/main.js"></script>
</body>
</html>

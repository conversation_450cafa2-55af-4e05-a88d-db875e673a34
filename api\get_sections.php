<?php
/**
 * API للحصول على الأقسام حسب الدائرة
 * Get Sections by Circle API
 */

require_once '../config/database.php';
require_once '../includes/auth.php';

// التحقق من تسجيل الدخول
requireLogin();

header('Content-Type: application/json; charset=utf-8');

try {
    $circleId = (int)($_GET['circle_id'] ?? 0);
    
    if ($circleId <= 0) {
        sendJsonResponse(['error' => 'معرف الدائرة مطلوب'], 400);
    }
    
    $db = getDB();
    $stmt = $db->prepare("
        SELECT id, section_name 
        FROM sections 
        WHERE circle_id = ? AND is_active = 1 
        ORDER BY section_order
    ");
    $stmt->execute([$circleId]);
    $sections = $stmt->fetchAll();
    
    sendJsonResponse($sections);
    
} catch (Exception $e) {
    logError("خطأ في جلب الأقسام: " . $e->getMessage());
    sendJsonResponse(['error' => 'خطأ في الخادم'], 500);
}
?>

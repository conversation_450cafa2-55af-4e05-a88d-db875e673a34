<?php
/**
 * إصلاح مسارات النظام
 * Fix System Paths
 */

echo "<h1>إصلاح مسارات النظام</h1>";

// قائمة الملفات التي تحتاج إصلاح
$filesToFix = [
    'includes/auth.php',
    'includes/navbar.php', 
    'includes/sidebar.php',
    'index.php',
    'login.php'
];

$fixes = 0;

foreach ($filesToFix as $file) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        $originalContent = $content;
        
        // إصلاح مسارات require_once
        $content = preg_replace(
            "/require_once\s+['\"]\.\.\/config\/database\.php['\"]/",
            "require_once __DIR__ . '/../config/database.php'",
            $content
        );
        
        $content = preg_replace(
            "/require_once\s+['\"]config\/database\.php['\"]/",
            "require_once __DIR__ . '/config/database.php'",
            $content
        );
        
        $content = preg_replace(
            "/require_once\s+['\"]includes\/auth\.php['\"]/",
            "require_once __DIR__ . '/includes/auth.php'",
            $content
        );
        
        // إصلاح مسارات include
        $content = preg_replace(
            "/include\s+['\"]includes\/navbar\.php['\"]/",
            "include __DIR__ . '/includes/navbar.php'",
            $content
        );
        
        $content = preg_replace(
            "/include\s+['\"]includes\/sidebar\.php['\"]/",
            "include __DIR__ . '/includes/sidebar.php'",
            $content
        );
        
        $content = preg_replace(
            "/include\s+['\"]\.\.\/\.\.\/includes\/navbar\.php['\"]/",
            "include __DIR__ . '/../../includes/navbar.php'",
            $content
        );
        
        $content = preg_replace(
            "/include\s+['\"]\.\.\/\.\.\/includes\/sidebar\.php['\"]/",
            "include __DIR__ . '/../../includes/sidebar.php'",
            $content
        );
        
        // حفظ الملف إذا تم تغييره
        if ($content !== $originalContent) {
            file_put_contents($file, $content);
            echo "✅ تم إصلاح: $file<br>";
            $fixes++;
        } else {
            echo "ℹ️ لا يحتاج إصلاح: $file<br>";
        }
    } else {
        echo "❌ ملف غير موجود: $file<br>";
    }
}

echo "<br><h2>النتيجة:</h2>";
echo "<p>تم إصلاح $fixes ملف</p>";

// إنشاء ملف تكوين بسيط
$configContent = '<?php
// إعدادات المسارات
define("ROOT_PATH", __DIR__);
define("INCLUDES_PATH", ROOT_PATH . "/includes");
define("CONFIG_PATH", ROOT_PATH . "/config");
define("ASSETS_PATH", ROOT_PATH . "/assets");
define("UPLOADS_PATH", ROOT_PATH . "/uploads");

// إعدادات URL
$protocol = isset($_SERVER["HTTPS"]) && $_SERVER["HTTPS"] === "on" ? "https" : "http";
$host = $_SERVER["HTTP_HOST"];
define("BASE_URL", $protocol . "://" . $host . "/ALarchef2/");
define("ASSETS_URL", BASE_URL . "assets/");
define("UPLOADS_URL", BASE_URL . "uploads/");
?>';

file_put_contents('config/paths.php', $configContent);
echo "✅ تم إنشاء ملف إعدادات المسارات<br>";

echo "<br><p><a href='login.php'>اختبار صفحة تسجيل الدخول</a></p>";
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح المسارات</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        h1, h2 { color: #333; }
    </style>
</head>
<body>
</body>
</html>

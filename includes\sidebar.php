<?php
/**
 * الشريط الجانبي
 * Sidebar Navigation
 */

if (!isset($currentUser)) {
    $auth = new Auth();
    $currentUser = $auth->getCurrentUser();
}

// تحديد الصفحة الحالية
$currentPage = basename($_SERVER['PHP_SELF']);
$currentDir = basename(dirname($_SERVER['PHP_SELF']));
?>

<div class="sidebar" id="sidebar">
    <div class="sidebar-header">
        <div class="sidebar-brand">
            <i class="bi bi-archive-fill me-2"></i>
            <span>نظام الأرشفة</span>
        </div>
    </div>
    
    <div class="sidebar-menu">
        <ul class="nav flex-column">
            <!-- لوحة التحكم -->
            <li class="nav-item">
                <a class="nav-link <?php echo ($currentPage == 'index.php') ? 'active' : ''; ?>" 
                   href="<?php echo BASE_URL; ?>index.php">
                    <i class="bi bi-speedometer2"></i>
                    <span>لوحة التحكم</span>
                </a>
            </li>
            
            <!-- شئون الموظفين -->
            <li class="nav-item">
                <a class="nav-link collapsed" href="#employeesSubmenu" data-bs-toggle="collapse" 
                   aria-expanded="false">
                    <i class="bi bi-people"></i>
                    <span>شئون الموظفين</span>
                    <i class="bi bi-chevron-down ms-auto"></i>
                </a>
                <div class="collapse <?php echo ($currentDir == 'employees') ? 'show' : ''; ?>" 
                     id="employeesSubmenu">
                    <ul class="nav flex-column ms-3">
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo BASE_URL; ?>pages/employees/list.php">
                                <i class="bi bi-list-ul"></i>
                                قائمة الموظفين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo BASE_URL; ?>pages/employees/add.php">
                                <i class="bi bi-person-plus"></i>
                                إضافة موظف جديد
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo BASE_URL; ?>pages/employees/import.php">
                                <i class="bi bi-file-earmark-excel"></i>
                                استيراد من Excel
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo BASE_URL; ?>pages/employees/search.php">
                                <i class="bi bi-search"></i>
                                البحث المتقدم
                            </a>
                        </li>
                    </ul>
                </div>
            </li>
            
            <!-- الحركات الوظيفية -->
            <li class="nav-item">
                <a class="nav-link collapsed" href="#movementsSubmenu" data-bs-toggle="collapse" 
                   aria-expanded="false">
                    <i class="bi bi-arrow-left-right"></i>
                    <span>الحركات الوظيفية</span>
                    <i class="bi bi-chevron-down ms-auto"></i>
                </a>
                <div class="collapse <?php echo ($currentDir == 'movements') ? 'show' : ''; ?>" 
                     id="movementsSubmenu">
                    <ul class="nav flex-column ms-3">
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo BASE_URL; ?>pages/movements/list.php">
                                <i class="bi bi-list-ul"></i>
                                قائمة الحركات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo BASE_URL; ?>pages/movements/add.php">
                                <i class="bi bi-plus-circle"></i>
                                إضافة حركة جديدة
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo BASE_URL; ?>pages/movements/reports.php">
                                <i class="bi bi-file-text"></i>
                                تقارير الحركات
                            </a>
                        </li>
                    </ul>
                </div>
            </li>
            
            <!-- أرشفة المستندات -->
            <li class="nav-item">
                <a class="nav-link collapsed" href="#documentsSubmenu" data-bs-toggle="collapse" 
                   aria-expanded="false">
                    <i class="bi bi-file-earmark-text"></i>
                    <span>أرشفة المستندات</span>
                    <i class="bi bi-chevron-down ms-auto"></i>
                </a>
                <div class="collapse <?php echo ($currentDir == 'documents') ? 'show' : ''; ?>" 
                     id="documentsSubmenu">
                    <ul class="nav flex-column ms-3">
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo BASE_URL; ?>pages/documents/list.php">
                                <i class="bi bi-list-ul"></i>
                                قائمة المستندات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo BASE_URL; ?>pages/documents/upload.php">
                                <i class="bi bi-cloud-upload"></i>
                                رفع مستند جديد
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo BASE_URL; ?>pages/documents/categories.php">
                                <i class="bi bi-tags"></i>
                                تصنيف المستندات
                            </a>
                        </li>
                    </ul>
                </div>
            </li>
            
            <!-- السكرتارية العامة -->
            <li class="nav-item">
                <a class="nav-link collapsed" href="#secretarySubmenu" data-bs-toggle="collapse" 
                   aria-expanded="false">
                    <i class="bi bi-envelope"></i>
                    <span>السكرتارية العامة</span>
                    <i class="bi bi-chevron-down ms-auto"></i>
                </a>
                <div class="collapse <?php echo (in_array($currentDir, ['correspondences', 'meetings'])) ? 'show' : ''; ?>" 
                     id="secretarySubmenu">
                    <ul class="nav flex-column ms-3">
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo BASE_URL; ?>pages/correspondences/list.php">
                                <i class="bi bi-envelope-open"></i>
                                المراسلات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo BASE_URL; ?>pages/meetings/list.php">
                                <i class="bi bi-calendar-event"></i>
                                محاضر الاجتماعات
                            </a>
                        </li>
                    </ul>
                </div>
            </li>
            
            <!-- الرعاية الطبية والتأمين -->
            <?php if ($currentUser['role'] == 'admin' || $currentUser['role'] == 'medical_staff'): ?>
            <li class="nav-item">
                <a class="nav-link collapsed" href="#medicalSubmenu" data-bs-toggle="collapse" 
                   aria-expanded="false">
                    <i class="bi bi-heart-pulse"></i>
                    <span>الرعاية الطبية</span>
                    <i class="bi bi-chevron-down ms-auto"></i>
                </a>
                <div class="collapse <?php echo ($currentDir == 'medical') ? 'show' : ''; ?>" 
                     id="medicalSubmenu">
                    <ul class="nav flex-column ms-3">
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo BASE_URL; ?>pages/medical/visits.php">
                                <i class="bi bi-hospital"></i>
                                الزيارات الطبية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo BASE_URL; ?>pages/medical/reports.php">
                                <i class="bi bi-file-medical"></i>
                                التقارير الطبية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo BASE_URL; ?>pages/medical/treatments.php">
                                <i class="bi bi-capsule"></i>
                                العلاجات والأدوية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo BASE_URL; ?>pages/insurance/list.php">
                                <i class="bi bi-shield-check"></i>
                                وثائق التأمين
                            </a>
                        </li>
                    </ul>
                </div>
            </li>
            <?php endif; ?>
            
            <!-- التقارير -->
            <li class="nav-item">
                <a class="nav-link collapsed" href="#reportsSubmenu" data-bs-toggle="collapse" 
                   aria-expanded="false">
                    <i class="bi bi-graph-up"></i>
                    <span>التقارير والإحصائيات</span>
                    <i class="bi bi-chevron-down ms-auto"></i>
                </a>
                <div class="collapse <?php echo ($currentDir == 'reports') ? 'show' : ''; ?>" 
                     id="reportsSubmenu">
                    <ul class="nav flex-column ms-3">
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo BASE_URL; ?>pages/reports/employees.php">
                                <i class="bi bi-people"></i>
                                تقارير الموظفين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo BASE_URL; ?>pages/reports/documents.php">
                                <i class="bi bi-file-text"></i>
                                تقارير المستندات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo BASE_URL; ?>pages/reports/retirement.php">
                                <i class="bi bi-calendar-x"></i>
                                تقارير التقاعد
                            </a>
                        </li>
                    </ul>
                </div>
            </li>
            
            <!-- الإعدادات (للمدير فقط) -->
            <?php if ($currentUser['role'] == 'admin'): ?>
            <li class="nav-item">
                <a class="nav-link collapsed" href="#settingsSubmenu" data-bs-toggle="collapse" 
                   aria-expanded="false">
                    <i class="bi bi-gear"></i>
                    <span>إعدادات النظام</span>
                    <i class="bi bi-chevron-down ms-auto"></i>
                </a>
                <div class="collapse <?php echo (in_array($currentDir, ['settings', 'users', 'backup'])) ? 'show' : ''; ?>" 
                     id="settingsSubmenu">
                    <ul class="nav flex-column ms-3">
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo BASE_URL; ?>pages/users/list.php">
                                <i class="bi bi-people"></i>
                                إدارة المستخدمين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo BASE_URL; ?>pages/settings/system.php">
                                <i class="bi bi-sliders"></i>
                                إعدادات عامة
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo BASE_URL; ?>pages/backup/index.php">
                                <i class="bi bi-hdd"></i>
                                النسخ الاحتياطي
                            </a>
                        </li>
                    </ul>
                </div>
            </li>
            <?php endif; ?>
        </ul>
    </div>
    
    <!-- Sidebar Footer -->
    <div class="sidebar-footer">
        <div class="text-center">
            <small class="text-muted">
                الإصدار <?php echo SYSTEM_VERSION; ?>
            </small>
        </div>
    </div>
</div>

<!-- Sidebar Overlay -->
<div class="sidebar-overlay" id="sidebarOverlay"></div>

<style>
.sidebar {
    position: fixed;
    top: 0;
    right: -280px;
    width: 280px;
    height: 100vh;
    background: #fff;
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
    transition: right 0.3s ease;
    z-index: 1040;
    overflow-y: auto;
}

.sidebar.show {
    right: 0;
}

.sidebar-header {
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
    background: #f8f9fa;
}

.sidebar-brand {
    font-weight: 600;
    font-size: 1.1rem;
    color: #495057;
}

.sidebar-menu {
    padding: 1rem 0;
}

.sidebar .nav-link {
    padding: 0.75rem 1rem;
    color: #495057;
    border-radius: 0;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
}

.sidebar .nav-link:hover {
    background-color: #f8f9fa;
    color: #0d6efd;
    transform: translateX(-5px);
}

.sidebar .nav-link.active {
    background-color: #0d6efd;
    color: white;
}

.sidebar .nav-link i {
    width: 20px;
    margin-left: 10px;
    text-align: center;
}

.sidebar .nav-link span {
    flex: 1;
}

.sidebar .collapse .nav-link {
    padding-right: 2rem;
    font-size: 0.9rem;
}

.sidebar-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 1rem;
    border-top: 1px solid #e9ecef;
    background: #f8f9fa;
}

.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 1035;
    display: none;
}

.sidebar-overlay.show {
    display: block;
}

.main-content {
    margin-top: 76px;
    padding: 2rem;
    transition: margin-right 0.3s ease;
}

@media (min-width: 992px) {
    .sidebar {
        right: 0;
        position: relative;
        height: calc(100vh - 76px);
        margin-top: 76px;
    }
    
    .main-content {
        margin-right: 280px;
    }
    
    .sidebar-overlay {
        display: none !important;
    }
}

@media (max-width: 991.98px) {
    .main-content {
        margin-right: 0;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const sidebarToggle = document.getElementById('sidebarToggle');
    const sidebar = document.getElementById('sidebar');
    const sidebarOverlay = document.getElementById('sidebarOverlay');
    
    // Toggle sidebar
    sidebarToggle.addEventListener('click', function() {
        sidebar.classList.toggle('show');
        sidebarOverlay.classList.toggle('show');
    });
    
    // Close sidebar when clicking overlay
    sidebarOverlay.addEventListener('click', function() {
        sidebar.classList.remove('show');
        sidebarOverlay.classList.remove('show');
    });
    
    // Close sidebar on window resize if mobile
    window.addEventListener('resize', function() {
        if (window.innerWidth >= 992) {
            sidebar.classList.remove('show');
            sidebarOverlay.classList.remove('show');
        }
    });
});
</script>

<?php
/**
 * صفحة قائمة الموظفين
 * Employees List Page
 */

require_once '../../config/database.php';
require_once '../../includes/auth.php';

// التحقق من تسجيل الدخول
requireLogin();

$auth = new Auth();
$currentUser = $auth->getCurrentUser();

// جلب الموظفين مع البيانات المرتبطة
try {
    $db = getDB();
    
    // إعداد الاستعلام مع الفلترة
    $whereClause = "WHERE ep.is_active = 1";
    $params = [];
    
    // فلترة حسب الإدارة
    if (isset($_GET['department']) && !empty($_GET['department'])) {
        $whereClause .= " AND d.id = ?";
        $params[] = (int)$_GET['department'];
    }
    
    // فلترة حسب نوع العمالة
    if (isset($_GET['employment_type']) && !empty($_GET['employment_type'])) {
        $whereClause .= " AND et.id = ?";
        $params[] = (int)$_GET['employment_type'];
    }
    
    // البحث في الاسم أو الرقم
    if (isset($_GET['search']) && !empty($_GET['search'])) {
        $search = '%' . $_GET['search'] . '%';
        $whereClause .= " AND (ep.full_name LIKE ? OR ep.employee_number LIKE ? OR ej.job_number LIKE ?)";
        $params[] = $search;
        $params[] = $search;
        $params[] = $search;
    }
    
    $stmt = $db->prepare("
        SELECT 
            ep.id,
            ep.employee_number,
            ep.full_name,
            ep.birth_date,
            ep.national_id,
            ep.gender,
            ep.phone_number,
            ej.job_number,
            ej.appointment_date,
            d.dept_name,
            c.circle_name,
            et.type_name as employment_type,
            jt.title_name as job_title,
            ej.level_number,
            ej.grade_number
        FROM employees_personal ep
        LEFT JOIN employees_job ej ON ep.id = ej.employee_id
        LEFT JOIN departments d ON ej.department_id = d.id
        LEFT JOIN circles c ON ej.circle_id = c.id
        LEFT JOIN employment_types et ON ej.employment_type_id = et.id
        LEFT JOIN job_titles jt ON ej.job_title_id = jt.id
        $whereClause
        ORDER BY ep.full_name ASC
    ");
    
    $stmt->execute($params);
    $employees = $stmt->fetchAll();
    
    // جلب الإدارات للفلترة
    $stmt = $db->query("SELECT id, dept_name FROM departments WHERE is_active = 1 ORDER BY dept_name");
    $departments = $stmt->fetchAll();
    
    // جلب أنواع العمالة للفلترة
    $stmt = $db->query("SELECT id, type_name FROM employment_types WHERE is_active = 1 ORDER BY type_order");
    $employmentTypes = $stmt->fetchAll();
    
} catch (Exception $e) {
    logError("خطأ في جلب الموظفين: " . $e->getMessage());
    $employees = [];
    $departments = [];
    $employmentTypes = [];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قائمة الموظفين - <?php echo SYSTEM_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/buttons/2.2.2/css/buttons.bootstrap5.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="../../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <?php include '../../includes/navbar.php'; ?>
    
    <!-- Sidebar -->
    <?php include '../../includes/sidebar.php'; ?>
    
    <!-- Main Content -->
    <div class="main-content">
        <div class="container-fluid">
            <!-- Page Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="page-header">
                        <h1 class="page-title">
                            <i class="bi bi-people me-2"></i>
                            قائمة الموظفين
                        </h1>
                        <p class="page-subtitle">
                            إدارة وعرض بيانات الموظفين
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- Action Buttons -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex gap-2 flex-wrap">
                        <a href="add.php" class="btn btn-primary">
                            <i class="bi bi-person-plus me-2"></i>
                            إضافة موظف جديد
                        </a>
                        <a href="import.php" class="btn btn-success">
                            <i class="bi bi-file-earmark-excel me-2"></i>
                            استيراد من Excel
                        </a>
                        <button type="button" class="btn btn-info" onclick="exportToExcel()">
                            <i class="bi bi-download me-2"></i>
                            تصدير إلى Excel
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="printList()">
                            <i class="bi bi-printer me-2"></i>
                            طباعة
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Filters -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="bi bi-funnel me-2"></i>
                                فلترة النتائج
                            </h6>
                        </div>
                        <div class="card-body">
                            <form method="GET" class="row g-3">
                                <div class="col-md-3">
                                    <label for="department" class="form-label">الإدارة</label>
                                    <select class="form-select" id="department" name="department">
                                        <option value="">جميع الإدارات</option>
                                        <?php foreach ($departments as $dept): ?>
                                            <option value="<?php echo $dept['id']; ?>" 
                                                    <?php echo (isset($_GET['department']) && $_GET['department'] == $dept['id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($dept['dept_name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="col-md-3">
                                    <label for="employment_type" class="form-label">نوع العمالة</label>
                                    <select class="form-select" id="employment_type" name="employment_type">
                                        <option value="">جميع الأنواع</option>
                                        <?php foreach ($employmentTypes as $type): ?>
                                            <option value="<?php echo $type['id']; ?>" 
                                                    <?php echo (isset($_GET['employment_type']) && $_GET['employment_type'] == $type['id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($type['type_name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="col-md-4">
                                    <label for="search" class="form-label">البحث</label>
                                    <input type="text" class="form-control" id="search" name="search" 
                                           placeholder="البحث في الاسم أو رقم الموظف..." 
                                           value="<?php echo htmlspecialchars($_GET['search'] ?? ''); ?>">
                                </div>
                                
                                <div class="col-md-2">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="bi bi-search"></i>
                                            بحث
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Statistics -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card stat-card stat-card-primary">
                        <div class="card-body">
                            <div class="stat-icon">
                                <i class="bi bi-people-fill"></i>
                            </div>
                            <div class="stat-content">
                                <h3 class="stat-number"><?php echo convertToArabicNumbers(count($employees)); ?></h3>
                                <p class="stat-label">إجمالي الموظفين</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card stat-card stat-card-success">
                        <div class="card-body">
                            <div class="stat-icon">
                                <i class="bi bi-person-check-fill"></i>
                            </div>
                            <div class="stat-content">
                                <h3 class="stat-number">
                                    <?php 
                                    $activeCount = count(array_filter($employees, function($emp) {
                                        return $emp['employment_type'] === 'رسمي';
                                    }));
                                    echo convertToArabicNumbers($activeCount);
                                    ?>
                                </h3>
                                <p class="stat-label">موظف رسمي</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card stat-card stat-card-warning">
                        <div class="card-body">
                            <div class="stat-icon">
                                <i class="bi bi-person-dash-fill"></i>
                            </div>
                            <div class="stat-content">
                                <h3 class="stat-number">
                                    <?php 
                                    $contractCount = count(array_filter($employees, function($emp) {
                                        return $emp['employment_type'] === 'متعاقد';
                                    }));
                                    echo convertToArabicNumbers($contractCount);
                                    ?>
                                </h3>
                                <p class="stat-label">موظف متعاقد</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card stat-card stat-card-info">
                        <div class="card-body">
                            <div class="stat-icon">
                                <i class="bi bi-calendar-x-fill"></i>
                            </div>
                            <div class="stat-content">
                                <h3 class="stat-number">
                                    <?php 
                                    $retiredCount = count(array_filter($employees, function($emp) {
                                        return $emp['employment_type'] === 'متقاعد';
                                    }));
                                    echo convertToArabicNumbers($retiredCount);
                                    ?>
                                </h3>
                                <p class="stat-label">موظف متقاعد</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Employees Table -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-list me-2"></i>
                                بيانات الموظفين
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table id="employeesTable" class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>رقم الموظف</th>
                                            <th>الاسم الكامل</th>
                                            <th>الرقم الوظيفي</th>
                                            <th>الإدارة</th>
                                            <th>الدائرة</th>
                                            <th>المسمى الوظيفي</th>
                                            <th>نوع العمالة</th>
                                            <th>تاريخ التعيين</th>
                                            <th>العمر</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($employees as $employee): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($employee['employee_number']); ?></td>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($employee['full_name']); ?></strong>
                                                    <br>
                                                    <small class="text-muted">
                                                        <i class="bi bi-<?php echo $employee['gender'] === 'ذكر' ? 'person' : 'person-dress'; ?>"></i>
                                                        <?php echo $employee['gender']; ?>
                                                    </small>
                                                </td>
                                                <td><?php echo htmlspecialchars($employee['job_number'] ?? '-'); ?></td>
                                                <td><?php echo htmlspecialchars($employee['dept_name'] ?? '-'); ?></td>
                                                <td><?php echo htmlspecialchars($employee['circle_name'] ?? '-'); ?></td>
                                                <td><?php echo htmlspecialchars($employee['job_title'] ?? '-'); ?></td>
                                                <td>
                                                    <span class="badge bg-<?php 
                                                        echo match($employee['employment_type']) {
                                                            'رسمي' => 'success',
                                                            'متعاقد' => 'warning',
                                                            'متقاعد' => 'secondary',
                                                            default => 'primary'
                                                        };
                                                    ?>">
                                                        <?php echo htmlspecialchars($employee['employment_type'] ?? '-'); ?>
                                                    </span>
                                                </td>
                                                <td><?php echo formatArabicDate($employee['appointment_date']); ?></td>
                                                <td>
                                                    <?php 
                                                    $age = calculateAge($employee['birth_date']);
                                                    echo convertToArabicNumbers($age) . ' سنة';
                                                    ?>
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <a href="view.php?id=<?php echo $employee['id']; ?>" 
                                                           class="btn btn-sm btn-outline-info" title="عرض">
                                                            <i class="bi bi-eye"></i>
                                                        </a>
                                                        <a href="edit.php?id=<?php echo $employee['id']; ?>" 
                                                           class="btn btn-sm btn-outline-primary" title="تعديل">
                                                            <i class="bi bi-pencil"></i>
                                                        </a>
                                                        <a href="../documents/list.php?employee_id=<?php echo $employee['id']; ?>" 
                                                           class="btn btn-sm btn-outline-success" title="المستندات">
                                                            <i class="bi bi-file-text"></i>
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.bootstrap5.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.html5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.print.min.js"></script>
    <script src="../../assets/js/main.js"></script>
    
    <script>
        // تهيئة DataTable
        $(document).ready(function() {
            $('#employeesTable').DataTable({
                language: {
                    url: 'https://cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json'
                },
                responsive: true,
                pageLength: 25,
                lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, 'الكل']],
                dom: 'Bfrtip',
                buttons: [
                    {
                        extend: 'excel',
                        text: 'تصدير Excel',
                        className: 'btn btn-success btn-sm'
                    },
                    {
                        extend: 'pdf',
                        text: 'تصدير PDF',
                        className: 'btn btn-danger btn-sm'
                    },
                    {
                        extend: 'print',
                        text: 'طباعة',
                        className: 'btn btn-info btn-sm'
                    }
                ]
            });
        });
        
        function exportToExcel() {
            $('#employeesTable').DataTable().button('.buttons-excel').trigger();
        }
        
        function printList() {
            $('#employeesTable').DataTable().button('.buttons-print').trigger();
        }
    </script>
</body>
</html>

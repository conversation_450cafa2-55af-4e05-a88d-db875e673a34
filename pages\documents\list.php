<?php
/**
 * صفحة قائمة المستندات
 * Documents List Page
 */

require_once '../../config/database.php';
require_once '../../includes/auth.php';

// التحقق من تسجيل الدخول
requireLogin();

$auth = new Auth();
$currentUser = $auth->getCurrentUser();

// جلب المستندات مع الفلترة
try {
    $db = getDB();
    
    // إعداد الاستعلام مع الفلترة
    $whereClause = "WHERE d.is_active = 1";
    $params = [];
    
    // فلترة حسب الموظف
    if (isset($_GET['employee_id']) && !empty($_GET['employee_id'])) {
        $whereClause .= " AND ep.id = ?";
        $params[] = (int)$_GET['employee_id'];
    }
    
    // فلترة حسب نوع المستند
    if (isset($_GET['document_type']) && !empty($_GET['document_type'])) {
        $whereClause .= " AND dt.id = ?";
        $params[] = (int)$_GET['document_type'];
    }
    
    // فلترة حسب الإدارة
    if (isset($_GET['department']) && !empty($_GET['department'])) {
        $whereClause .= " AND dept.id = ?";
        $params[] = (int)$_GET['department'];
    }
    
    // البحث في العنوان
    if (isset($_GET['search']) && !empty($_GET['search'])) {
        $search = '%' . $_GET['search'] . '%';
        $whereClause .= " AND (d.document_title LIKE ? OR ep.full_name LIKE ? OR ep.employee_number LIKE ?)";
        $params[] = $search;
        $params[] = $search;
        $params[] = $search;
    }
    
    $stmt = $db->prepare("
        SELECT 
            d.id,
            d.document_title,
            d.file_name,
            d.file_path,
            d.file_size,
            d.file_type,
            d.upload_date,
            ep.full_name as employee_name,
            ep.employee_number,
            dt.type_name as document_type,
            dept.dept_name,
            u.full_name as uploaded_by_name
        FROM documents d
        JOIN employees_personal ep ON d.employee_id = ep.id
        JOIN document_types dt ON d.document_type_id = dt.id
        LEFT JOIN employees_job ej ON ep.id = ej.employee_id
        LEFT JOIN departments dept ON ej.department_id = dept.id
        JOIN users u ON d.uploaded_by = u.id
        $whereClause
        ORDER BY d.upload_date DESC
    ");
    
    $stmt->execute($params);
    $documents = $stmt->fetchAll();
    
    // جلب أنواع المستندات للفلترة
    $stmt = $db->query("SELECT id, type_name FROM document_types WHERE is_active = 1 ORDER BY type_name");
    $documentTypes = $stmt->fetchAll();
    
    // جلب الإدارات للفلترة
    $stmt = $db->query("SELECT id, dept_name FROM departments WHERE is_active = 1 ORDER BY dept_name");
    $departments = $stmt->fetchAll();
    
} catch (Exception $e) {
    logError("خطأ في جلب المستندات: " . $e->getMessage());
    $documents = [];
    $documentTypes = [];
    $departments = [];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قائمة المستندات - <?php echo SYSTEM_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/buttons/2.2.2/css/buttons.bootstrap5.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="../../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <?php include '../../includes/navbar.php'; ?>
    
    <!-- Sidebar -->
    <?php include '../../includes/sidebar.php'; ?>
    
    <!-- Main Content -->
    <div class="main-content">
        <div class="container-fluid">
            <!-- Page Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="page-header">
                        <h1 class="page-title">
                            <i class="bi bi-file-earmark-text me-2"></i>
                            قائمة المستندات
                        </h1>
                        <p class="page-subtitle">
                            إدارة وعرض المستندات المؤرشفة
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- Action Buttons -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex gap-2 flex-wrap">
                        <a href="upload.php" class="btn btn-primary">
                            <i class="bi bi-cloud-upload me-2"></i>
                            رفع مستند جديد
                        </a>
                        <button type="button" class="btn btn-success" onclick="exportToExcel()">
                            <i class="bi bi-file-earmark-excel me-2"></i>
                            تصدير إلى Excel
                        </button>
                        <button type="button" class="btn btn-info" onclick="printList()">
                            <i class="bi bi-printer me-2"></i>
                            طباعة
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Filters -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="bi bi-funnel me-2"></i>
                                فلترة النتائج
                            </h6>
                        </div>
                        <div class="card-body">
                            <form method="GET" class="row g-3">
                                <div class="col-md-3">
                                    <label for="document_type" class="form-label">نوع المستند</label>
                                    <select class="form-select" id="document_type" name="document_type">
                                        <option value="">جميع الأنواع</option>
                                        <?php foreach ($documentTypes as $type): ?>
                                            <option value="<?php echo $type['id']; ?>" 
                                                    <?php echo (isset($_GET['document_type']) && $_GET['document_type'] == $type['id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($type['type_name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="col-md-3">
                                    <label for="department" class="form-label">الإدارة</label>
                                    <select class="form-select" id="department" name="department">
                                        <option value="">جميع الإدارات</option>
                                        <?php foreach ($departments as $dept): ?>
                                            <option value="<?php echo $dept['id']; ?>" 
                                                    <?php echo (isset($_GET['department']) && $_GET['department'] == $dept['id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($dept['dept_name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="col-md-4">
                                    <label for="search" class="form-label">البحث</label>
                                    <input type="text" class="form-control" id="search" name="search" 
                                           placeholder="البحث في عنوان المستند أو اسم الموظف..." 
                                           value="<?php echo htmlspecialchars($_GET['search'] ?? ''); ?>">
                                </div>
                                
                                <div class="col-md-2">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="bi bi-search"></i>
                                            بحث
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Statistics -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card stat-card stat-card-primary">
                        <div class="card-body">
                            <div class="stat-icon">
                                <i class="bi bi-file-earmark-text-fill"></i>
                            </div>
                            <div class="stat-content">
                                <h3 class="stat-number"><?php echo convertToArabicNumbers(count($documents)); ?></h3>
                                <p class="stat-label">إجمالي المستندات</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card stat-card stat-card-success">
                        <div class="card-body">
                            <div class="stat-icon">
                                <i class="bi bi-file-earmark-pdf-fill"></i>
                            </div>
                            <div class="stat-content">
                                <h3 class="stat-number">
                                    <?php 
                                    $pdfCount = count(array_filter($documents, function($doc) {
                                        return $doc['file_type'] === 'pdf';
                                    }));
                                    echo convertToArabicNumbers($pdfCount);
                                    ?>
                                </h3>
                                <p class="stat-label">ملفات PDF</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card stat-card stat-card-info">
                        <div class="card-body">
                            <div class="stat-icon">
                                <i class="bi bi-image-fill"></i>
                            </div>
                            <div class="stat-content">
                                <h3 class="stat-number">
                                    <?php 
                                    $imageCount = count(array_filter($documents, function($doc) {
                                        return in_array($doc['file_type'], ['jpg', 'jpeg', 'png']);
                                    }));
                                    echo convertToArabicNumbers($imageCount);
                                    ?>
                                </h3>
                                <p class="stat-label">الصور</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card stat-card stat-card-warning">
                        <div class="card-body">
                            <div class="stat-icon">
                                <i class="bi bi-calendar-month"></i>
                            </div>
                            <div class="stat-content">
                                <h3 class="stat-number">
                                    <?php 
                                    $thisMonthCount = count(array_filter($documents, function($doc) {
                                        return date('Y-m', strtotime($doc['upload_date'])) === date('Y-m');
                                    }));
                                    echo convertToArabicNumbers($thisMonthCount);
                                    ?>
                                </h3>
                                <p class="stat-label">هذا الشهر</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Documents Table -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-list me-2"></i>
                                المستندات المؤرشفة
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table id="documentsTable" class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>عنوان المستند</th>
                                            <th>اسم الموظف</th>
                                            <th>نوع المستند</th>
                                            <th>الإدارة</th>
                                            <th>نوع الملف</th>
                                            <th>حجم الملف</th>
                                            <th>تاريخ الرفع</th>
                                            <th>رفع بواسطة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($documents as $document): ?>
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <i class="bi bi-file-earmark-<?php 
                                                            echo match($document['file_type']) {
                                                                'pdf' => 'pdf text-danger',
                                                                'doc', 'docx' => 'word text-primary',
                                                                'xls', 'xlsx' => 'excel text-success',
                                                                'jpg', 'jpeg', 'png' => 'image text-warning',
                                                                default => 'text text-secondary'
                                                            };
                                                        ?> me-2"></i>
                                                        <div>
                                                            <strong><?php echo htmlspecialchars($document['document_title']); ?></strong>
                                                            <br>
                                                            <small class="text-muted"><?php echo htmlspecialchars($document['file_name']); ?></small>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($document['employee_name']); ?></strong>
                                                    <br>
                                                    <small class="text-muted"><?php echo htmlspecialchars($document['employee_number']); ?></small>
                                                </td>
                                                <td>
                                                    <span class="badge bg-primary">
                                                        <?php echo htmlspecialchars($document['document_type']); ?>
                                                    </span>
                                                </td>
                                                <td><?php echo htmlspecialchars($document['dept_name'] ?? '-'); ?></td>
                                                <td>
                                                    <span class="badge bg-secondary">
                                                        <?php echo strtoupper($document['file_type']); ?>
                                                    </span>
                                                </td>
                                                <td><?php echo formatFileSize($document['file_size']); ?></td>
                                                <td><?php echo formatArabicDate($document['upload_date']); ?></td>
                                                <td><?php echo htmlspecialchars($document['uploaded_by_name']); ?></td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <a href="view.php?id=<?php echo $document['id']; ?>" 
                                                           class="btn btn-sm btn-outline-info" title="عرض">
                                                            <i class="bi bi-eye"></i>
                                                        </a>
                                                        <a href="../../<?php echo $document['file_path']; ?>" 
                                                           class="btn btn-sm btn-outline-success" title="تحميل" target="_blank">
                                                            <i class="bi bi-download"></i>
                                                        </a>
                                                        <a href="edit.php?id=<?php echo $document['id']; ?>" 
                                                           class="btn btn-sm btn-outline-primary" title="تعديل">
                                                            <i class="bi bi-pencil"></i>
                                                        </a>
                                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                                title="حذف" onclick="deleteDocument(<?php echo $document['id']; ?>)">
                                                            <i class="bi bi-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.bootstrap5.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.html5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.print.min.js"></script>
    <script src="../../assets/js/main.js"></script>
    
    <script>
        // تهيئة DataTable
        $(document).ready(function() {
            $('#documentsTable').DataTable({
                language: {
                    url: 'https://cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json'
                },
                responsive: true,
                pageLength: 25,
                lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, 'الكل']],
                dom: 'Bfrtip',
                buttons: [
                    {
                        extend: 'excel',
                        text: 'تصدير Excel',
                        className: 'btn btn-success btn-sm'
                    },
                    {
                        extend: 'pdf',
                        text: 'تصدير PDF',
                        className: 'btn btn-danger btn-sm'
                    },
                    {
                        extend: 'print',
                        text: 'طباعة',
                        className: 'btn btn-info btn-sm'
                    }
                ],
                order: [[6, 'desc']] // ترتيب حسب تاريخ الرفع
            });
        });
        
        function exportToExcel() {
            $('#documentsTable').DataTable().button('.buttons-excel').trigger();
        }
        
        function printList() {
            $('#documentsTable').DataTable().button('.buttons-print').trigger();
        }
        
        // حذف مستند
        async function deleteDocument(documentId) {
            const confirmed = await ALarchef.confirmDelete('هل أنت متأكد من حذف هذا المستند؟');
            if (confirmed) {
                try {
                    const response = await fetch(`../../api/documents.php?id=${documentId}`, {
                        method: 'DELETE'
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        ALarchef.showMessage('تم حذف المستند بنجاح', 'success');
                        setTimeout(() => {
                            location.reload();
                        }, 1500);
                    } else {
                        ALarchef.showMessage(result.message || 'فشل في حذف المستند', 'error');
                    }
                } catch (error) {
                    ALarchef.showMessage('حدث خطأ في الاتصال', 'error');
                }
            }
        }
    </script>
</body>
</html>

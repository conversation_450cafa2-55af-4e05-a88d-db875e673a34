<?php
/**
 * نظام المصادقة وإدارة الجلسات
 * Authentication and Session Management System
 */

require_once '../config/database.php';

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

class Auth {
    private $db;
    
    public function __construct() {
        $this->db = getDB();
    }
    
    /**
     * تسجيل دخول المستخدم
     */
    public function login($username, $password) {
        try {
            $stmt = $this->db->prepare("
                SELECT id, username, password, full_name, email, role, is_active 
                FROM users 
                WHERE username = ? AND is_active = 1
            ");
            $stmt->execute([$username]);
            $user = $stmt->fetch();
            
            if ($user && verifyPassword($password, $user['password'])) {
                // إنشاء جلسة المستخدم
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['full_name'] = $user['full_name'];
                $_SESSION['email'] = $user['email'];
                $_SESSION['role'] = $user['role'];
                $_SESSION['login_time'] = time();
                $_SESSION['last_activity'] = time();
                
                // تسجيل عملية تسجيل الدخول
                $this->logUserActivity($user['id'], 'login', 'تسجيل دخول ناجح');
                
                return [
                    'success' => true,
                    'message' => 'تم تسجيل الدخول بنجاح',
                    'user' => [
                        'id' => $user['id'],
                        'username' => $user['username'],
                        'full_name' => $user['full_name'],
                        'role' => $user['role']
                    ]
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'اسم المستخدم أو كلمة المرور غير صحيحة'
                ];
            }
        } catch (Exception $e) {
            logError("خطأ في تسجيل الدخول: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'حدث خطأ في النظام'
            ];
        }
    }
    
    /**
     * تسجيل خروج المستخدم
     */
    public function logout() {
        if (isset($_SESSION['user_id'])) {
            $this->logUserActivity($_SESSION['user_id'], 'logout', 'تسجيل خروج');
        }
        
        session_destroy();
        return [
            'success' => true,
            'message' => 'تم تسجيل الخروج بنجاح'
        ];
    }
    
    /**
     * التحقق من تسجيل دخول المستخدم
     */
    public function isLoggedIn() {
        if (!isset($_SESSION['user_id']) || !isset($_SESSION['last_activity'])) {
            return false;
        }
        
        // التحقق من انتهاء مهلة الجلسة
        if (SESSION_TIMEOUT > 0) {
            if (time() - $_SESSION['last_activity'] > SESSION_TIMEOUT) {
                $this->logout();
                return false;
            }
        }
        
        // تحديث وقت آخر نشاط
        $_SESSION['last_activity'] = time();
        
        return true;
    }
    
    /**
     * الحصول على بيانات المستخدم الحالي
     */
    public function getCurrentUser() {
        if (!$this->isLoggedIn()) {
            return null;
        }
        
        return [
            'id' => $_SESSION['user_id'],
            'username' => $_SESSION['username'],
            'full_name' => $_SESSION['full_name'],
            'email' => $_SESSION['email'],
            'role' => $_SESSION['role']
        ];
    }
    
    /**
     * التحقق من صلاحية المستخدم
     */
    public function hasPermission($requiredRole) {
        if (!$this->isLoggedIn()) {
            return false;
        }
        
        $userRole = $_SESSION['role'];
        
        // ترتيب الأدوار حسب الصلاحيات
        $roleHierarchy = [
            'employee' => 1,
            'medical_staff' => 2,
            'archive_staff' => 3,
            'admin' => 4
        ];
        
        $userLevel = $roleHierarchy[$userRole] ?? 0;
        $requiredLevel = $roleHierarchy[$requiredRole] ?? 0;
        
        return $userLevel >= $requiredLevel;
    }
    
    /**
     * إنشاء مستخدم جديد
     */
    public function createUser($userData) {
        try {
            // التحقق من عدم وجود اسم المستخدم
            $stmt = $this->db->prepare("SELECT id FROM users WHERE username = ?");
            $stmt->execute([$userData['username']]);
            
            if ($stmt->fetch()) {
                return [
                    'success' => false,
                    'message' => 'اسم المستخدم موجود بالفعل'
                ];
            }
            
            // إنشاء المستخدم
            $stmt = $this->db->prepare("
                INSERT INTO users (username, password, full_name, email, role, is_active) 
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            
            $hashedPassword = hashPassword($userData['password']);
            
            $stmt->execute([
                $userData['username'],
                $hashedPassword,
                $userData['full_name'],
                $userData['email'],
                $userData['role'],
                $userData['is_active'] ?? 1
            ]);
            
            $userId = $this->db->lastInsertId();
            
            // تسجيل العملية
            $this->logUserActivity($_SESSION['user_id'], 'create_user', "إنشاء مستخدم جديد: {$userData['username']}");
            
            return [
                'success' => true,
                'message' => 'تم إنشاء المستخدم بنجاح',
                'user_id' => $userId
            ];
            
        } catch (Exception $e) {
            logError("خطأ في إنشاء المستخدم: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'حدث خطأ في إنشاء المستخدم'
            ];
        }
    }
    
    /**
     * تحديث بيانات المستخدم
     */
    public function updateUser($userId, $userData) {
        try {
            $updateFields = [];
            $params = [];
            
            if (isset($userData['full_name'])) {
                $updateFields[] = "full_name = ?";
                $params[] = $userData['full_name'];
            }
            
            if (isset($userData['email'])) {
                $updateFields[] = "email = ?";
                $params[] = $userData['email'];
            }
            
            if (isset($userData['role'])) {
                $updateFields[] = "role = ?";
                $params[] = $userData['role'];
            }
            
            if (isset($userData['is_active'])) {
                $updateFields[] = "is_active = ?";
                $params[] = $userData['is_active'];
            }
            
            if (isset($userData['password']) && !empty($userData['password'])) {
                $updateFields[] = "password = ?";
                $params[] = hashPassword($userData['password']);
            }
            
            if (empty($updateFields)) {
                return [
                    'success' => false,
                    'message' => 'لا توجد بيانات للتحديث'
                ];
            }
            
            $updateFields[] = "updated_at = CURRENT_TIMESTAMP";
            $params[] = $userId;
            
            $sql = "UPDATE users SET " . implode(', ', $updateFields) . " WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            
            // تسجيل العملية
            $this->logUserActivity($_SESSION['user_id'], 'update_user', "تحديث بيانات المستخدم ID: $userId");
            
            return [
                'success' => true,
                'message' => 'تم تحديث بيانات المستخدم بنجاح'
            ];
            
        } catch (Exception $e) {
            logError("خطأ في تحديث المستخدم: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'حدث خطأ في تحديث البيانات'
            ];
        }
    }
    
    /**
     * تسجيل نشاط المستخدم
     */
    private function logUserActivity($userId, $action, $description) {
        try {
            $stmt = $this->db->prepare("
                INSERT INTO user_activity_log (user_id, action, description, ip_address, user_agent, created_at) 
                VALUES (?, ?, ?, ?, ?, NOW())
            ");
            
            $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '';
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
            
            $stmt->execute([$userId, $action, $description, $ipAddress, $userAgent]);
        } catch (Exception $e) {
            logError("خطأ في تسجيل نشاط المستخدم: " . $e->getMessage());
        }
    }
    
    /**
     * الحصول على قائمة المستخدمين
     */
    public function getUsers($limit = 50, $offset = 0) {
        try {
            $stmt = $this->db->prepare("
                SELECT id, username, full_name, email, role, is_active, created_at 
                FROM users 
                ORDER BY created_at DESC 
                LIMIT ? OFFSET ?
            ");
            $stmt->execute([$limit, $offset]);
            
            return $stmt->fetchAll();
        } catch (Exception $e) {
            logError("خطأ في جلب المستخدمين: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * حذف مستخدم
     */
    public function deleteUser($userId) {
        try {
            // التأكد من عدم حذف المستخدم الحالي
            if ($userId == $_SESSION['user_id']) {
                return [
                    'success' => false,
                    'message' => 'لا يمكن حذف المستخدم الحالي'
                ];
            }
            
            $stmt = $this->db->prepare("UPDATE users SET is_active = 0 WHERE id = ?");
            $stmt->execute([$userId]);
            
            // تسجيل العملية
            $this->logUserActivity($_SESSION['user_id'], 'delete_user', "حذف المستخدم ID: $userId");
            
            return [
                'success' => true,
                'message' => 'تم حذف المستخدم بنجاح'
            ];
            
        } catch (Exception $e) {
            logError("خطأ في حذف المستخدم: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'حدث خطأ في حذف المستخدم'
            ];
        }
    }
}

// دالة للتحقق من تسجيل الدخول
function requireLogin() {
    $auth = new Auth();
    if (!$auth->isLoggedIn()) {
        header('Location: ../login.php');
        exit;
    }
}

// دالة للتحقق من الصلاحيات
function requirePermission($role) {
    $auth = new Auth();
    if (!$auth->hasPermission($role)) {
        header('HTTP/1.1 403 Forbidden');
        die('ليس لديك صلاحية للوصول إلى هذه الصفحة');
    }
}

// إنشاء جدول سجل نشاط المستخدمين إذا لم يكن موجود
try {
    $db = getDB();
    $db->exec("
        CREATE TABLE IF NOT EXISTS user_activity_log (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            action VARCHAR(100) NOT NULL,
            description TEXT,
            ip_address VARCHAR(45),
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id)
        )
    ");
} catch (Exception $e) {
    logError("خطأ في إنشاء جدول سجل النشاط: " . $e->getMessage());
}

?>

<?php
/**
 * صفحة رفع مستند جديد
 * Upload New Document Page
 */

require_once '../../config/database.php';
require_once '../../includes/auth.php';

// التحقق من تسجيل الدخول
requireLogin();

$auth = new Auth();
$currentUser = $auth->getCurrentUser();

$message = '';
$messageType = 'info';

// معالجة رفع المستند
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db = getDB();
        
        // التحقق من صحة البيانات
        $errors = [];
        
        $employeeId = (int)$_POST['employee_id'];
        $documentTypeId = (int)$_POST['document_type_id'];
        $documentTitle = sanitize($_POST['document_title']);
        $documentDescription = sanitize($_POST['document_description']);
        
        if ($employeeId <= 0) {
            $errors[] = 'يرجى اختيار الموظف';
        }
        
        if ($documentTypeId <= 0) {
            $errors[] = 'يرجى اختيار نوع المستند';
        }
        
        if (empty($documentTitle)) {
            $errors[] = 'عنوان المستند مطلوب';
        }
        
        // التحقق من رفع الملف
        if (!isset($_FILES['document_file']) || $_FILES['document_file']['error'] !== UPLOAD_ERR_OK) {
            $errors[] = 'يرجى اختيار ملف للرفع';
        } else {
            $file = $_FILES['document_file'];
            
            // التحقق من نوع الملف
            if (!isAllowedFileType($file['name'])) {
                $errors[] = 'نوع الملف غير مسموح. الأنواع المسموحة: ' . implode(', ', ALLOWED_FILE_TYPES);
            }
            
            // التحقق من حجم الملف
            if ($file['size'] > MAX_FILE_SIZE) {
                $errors[] = 'حجم الملف كبير جداً. الحد الأقصى: ' . formatFileSize(MAX_FILE_SIZE);
            }
        }
        
        if (empty($errors)) {
            // إنشاء مجلد الرفع
            $uploadDir = '../../uploads/documents/';
            createDirectoryIfNotExists($uploadDir);
            
            // إنشاء اسم ملف فريد
            $fileExtension = getFileType($file['name']);
            $fileName = time() . '_' . uniqid() . '.' . $fileExtension;
            $filePath = $uploadDir . $fileName;
            
            // رفع الملف
            if (move_uploaded_file($file['tmp_name'], $filePath)) {
                // حفظ بيانات المستند في قاعدة البيانات
                $stmt = $db->prepare("
                    INSERT INTO documents 
                    (employee_id, document_type_id, document_title, document_description, 
                     file_name, file_path, file_size, file_type, uploaded_by) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ");
                
                $stmt->execute([
                    $employeeId,
                    $documentTypeId,
                    $documentTitle,
                    $documentDescription,
                    $file['name'],
                    'uploads/documents/' . $fileName,
                    $file['size'],
                    $fileExtension,
                    $currentUser['id']
                ]);
                
                $documentId = $db->lastInsertId();
                
                $message = 'تم رفع المستند بنجاح';
                $messageType = 'success';
                
                // إعادة توجيه إلى صفحة عرض المستند
                header("Location: view.php?id=$documentId&message=" . urlencode($message));
                exit;
                
            } else {
                $errors[] = 'فشل في رفع الملف';
            }
        }
        
        if (!empty($errors)) {
            $message = implode('<br>', $errors);
            $messageType = 'danger';
        }
        
    } catch (Exception $e) {
        logError("خطأ في رفع المستند: " . $e->getMessage());
        $message = 'حدث خطأ في رفع المستند';
        $messageType = 'danger';
    }
}

// جلب البيانات المرجعية
try {
    $db = getDB();
    
    // جلب أنواع المستندات
    $stmt = $db->query("SELECT id, type_name FROM document_types WHERE is_active = 1 ORDER BY type_name");
    $documentTypes = $stmt->fetchAll();
    
} catch (Exception $e) {
    logError("خطأ في جلب أنواع المستندات: " . $e->getMessage());
    $documentTypes = [];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>رفع مستند جديد - <?php echo SYSTEM_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="../../assets/css/style.css" rel="stylesheet">
    
    <style>
        .file-drop-zone {
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            padding: 3rem;
            text-align: center;
            background: #f8f9fa;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .file-drop-zone:hover {
            border-color: #0d6efd;
            background: rgba(13, 110, 253, 0.05);
        }
        
        .file-drop-zone.dragover {
            border-color: #0d6efd;
            background: rgba(13, 110, 253, 0.1);
        }
        
        .file-info {
            display: none;
            background: #e7f3ff;
            border: 1px solid #b3d7ff;
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
        }
        
        .file-preview {
            max-width: 100px;
            max-height: 100px;
            object-fit: cover;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <?php include '../../includes/navbar.php'; ?>
    
    <!-- Sidebar -->
    <?php include '../../includes/sidebar.php'; ?>
    
    <!-- Main Content -->
    <div class="main-content">
        <div class="container-fluid">
            <!-- Page Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="page-header">
                        <h1 class="page-title">
                            <i class="bi bi-cloud-upload me-2"></i>
                            رفع مستند جديد
                        </h1>
                        <p class="page-subtitle">
                            إضافة مستند جديد إلى أرشيف الموظفين
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- Messages -->
            <?php if (!empty($message)): ?>
                <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                    <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <!-- Upload Form -->
            <form method="POST" enctype="multipart/form-data" class="needs-validation" novalidate>
                <div class="row">
                    <!-- معلومات المستند -->
                    <div class="col-lg-8 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-info-circle me-2"></i>
                                    معلومات المستند
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="employee_id" class="form-label">الموظف *</label>
                                    <select class="form-select" id="employee_id" name="employee_id" required>
                                        <option value="">ابحث عن الموظف...</option>
                                    </select>
                                    <div class="invalid-feedback">يرجى اختيار الموظف</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="document_type_id" class="form-label">نوع المستند *</label>
                                    <select class="form-select" id="document_type_id" name="document_type_id" required>
                                        <option value="">اختر نوع المستند</option>
                                        <?php foreach ($documentTypes as $type): ?>
                                            <option value="<?php echo $type['id']; ?>" 
                                                    <?php echo (($_POST['document_type_id'] ?? '') == $type['id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($type['type_name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="invalid-feedback">يرجى اختيار نوع المستند</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="document_title" class="form-label">عنوان المستند *</label>
                                    <input type="text" class="form-control" id="document_title" 
                                           name="document_title" required 
                                           value="<?php echo htmlspecialchars($_POST['document_title'] ?? ''); ?>">
                                    <div class="invalid-feedback">يرجى إدخال عنوان المستند</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="document_description" class="form-label">وصف المستند</label>
                                    <textarea class="form-control" id="document_description" 
                                              name="document_description" rows="3"><?php echo htmlspecialchars($_POST['document_description'] ?? ''); ?></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- رفع الملف -->
                    <div class="col-lg-4 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-file-earmark-arrow-up me-2"></i>
                                    رفع الملف
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="file-drop-zone" onclick="document.getElementById('document_file').click()">
                                    <i class="bi bi-cloud-upload display-4 text-muted mb-3"></i>
                                    <h5>اسحب الملف هنا أو انقر للاختيار</h5>
                                    <p class="text-muted mb-0">
                                        الأنواع المسموحة: PDF, DOC, DOCX, XLS, XLSX, JPG, PNG<br>
                                        الحد الأقصى: <?php echo formatFileSize(MAX_FILE_SIZE); ?>
                                    </p>
                                </div>
                                
                                <input type="file" class="d-none" id="document_file" name="document_file" 
                                       accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png" required>
                                
                                <div class="file-info" id="file-info">
                                    <div class="d-flex align-items-center">
                                        <div class="file-icon me-3">
                                            <i class="bi bi-file-earmark display-6"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1" id="file-name"></h6>
                                            <small class="text-muted" id="file-size"></small>
                                        </div>
                                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="clearFile()">
                                            <i class="bi bi-x"></i>
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="invalid-feedback">
                                    يرجى اختيار ملف للرفع
                                </div>
                            </div>
                        </div>
                        
                        <!-- معلومات إضافية -->
                        <div class="card mt-3">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="bi bi-info-circle me-2"></i>
                                    معلومات مهمة
                                </h6>
                                <ul class="list-unstyled small text-muted mb-0">
                                    <li><i class="bi bi-check text-success me-1"></i> تأكد من وضوح المستند</li>
                                    <li><i class="bi bi-check text-success me-1"></i> استخدم أسماء ملفات واضحة</li>
                                    <li><i class="bi bi-check text-success me-1"></i> تحقق من صحة البيانات</li>
                                    <li><i class="bi bi-shield-check text-primary me-1"></i> الملفات محمية ومشفرة</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body">
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-cloud-upload me-2"></i>
                                        رفع المستند
                                    </button>
                                    <a href="list.php" class="btn btn-secondary">
                                        <i class="bi bi-arrow-right me-2"></i>
                                        العودة إلى القائمة
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="../../assets/js/main.js"></script>
    
    <script>
        $(document).ready(function() {
            // تهيئة Select2 للموظفين
            $('#employee_id').select2({
                theme: 'bootstrap-5',
                placeholder: 'ابحث عن الموظف...',
                allowClear: true,
                ajax: {
                    url: '../../api/search_employees.php',
                    dataType: 'json',
                    delay: 250,
                    data: function (params) {
                        return {
                            q: params.term,
                            page: params.page
                        };
                    },
                    processResults: function (data, params) {
                        params.page = params.page || 1;
                        
                        return {
                            results: data.items,
                            pagination: {
                                more: (params.page * 30) < data.total_count
                            }
                        };
                    },
                    cache: true
                }
            });
            
            // معالجة تغيير الملف
            $('#document_file').on('change', function() {
                handleFileSelect(this.files[0]);
            });
            
            // معالجة السحب والإفلات
            const dropZone = document.querySelector('.file-drop-zone');
            
            dropZone.addEventListener('dragover', function(e) {
                e.preventDefault();
                this.classList.add('dragover');
            });
            
            dropZone.addEventListener('dragleave', function(e) {
                e.preventDefault();
                this.classList.remove('dragover');
            });
            
            dropZone.addEventListener('drop', function(e) {
                e.preventDefault();
                this.classList.remove('dragover');
                
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    document.getElementById('document_file').files = files;
                    handleFileSelect(files[0]);
                }
            });
        });
        
        // معالجة اختيار الملف
        function handleFileSelect(file) {
            if (!file) return;
            
            // التحقق من نوع الملف
            const allowedTypes = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'jpg', 'jpeg', 'png'];
            const fileExtension = file.name.split('.').pop().toLowerCase();
            
            if (!allowedTypes.includes(fileExtension)) {
                ALarchef.showMessage('نوع الملف غير مسموح', 'error');
                clearFile();
                return;
            }
            
            // التحقق من حجم الملف
            if (file.size > <?php echo MAX_FILE_SIZE; ?>) {
                ALarchef.showMessage('حجم الملف كبير جداً', 'error');
                clearFile();
                return;
            }
            
            // عرض معلومات الملف
            document.getElementById('file-name').textContent = file.name;
            document.getElementById('file-size').textContent = ALarchef.formatFileSize(file.size);
            
            // تحديث أيقونة الملف
            const fileIcon = document.querySelector('.file-info .file-icon i');
            fileIcon.className = `bi bi-file-earmark-${getFileIcon(fileExtension)} display-6`;
            
            // إظهار معلومات الملف
            document.getElementById('file-info').style.display = 'block';
            document.querySelector('.file-drop-zone').style.display = 'none';
        }
        
        // مسح الملف المختار
        function clearFile() {
            document.getElementById('document_file').value = '';
            document.getElementById('file-info').style.display = 'none';
            document.querySelector('.file-drop-zone').style.display = 'block';
        }
        
        // الحصول على أيقونة الملف
        function getFileIcon(extension) {
            const icons = {
                'pdf': 'pdf',
                'doc': 'word',
                'docx': 'word',
                'xls': 'excel',
                'xlsx': 'excel',
                'jpg': 'image',
                'jpeg': 'image',
                'png': 'image'
            };
            
            return icons[extension] || 'text';
        }
    </script>
</body>
</html>

# نظام أرشفة مصنع أسمنت البرح - إعدادات Apache
# ALarchef Archive System - Apache Configuration

# تفعيل محرك إعادة الكتابة
RewriteEngine On

# منع الوصول إلى الملفات الحساسة
<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|inc|bak|sql)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# منع الوصول إلى مجلدات النظام
RedirectMatch 403 ^/ALarchef2/(config|includes|logs|database)/.*$

# حماية ملفات النسخ الاحتياطي
<Files "*.sql">
    Order Allow,Deny
    Deny from all
</Files>

# منع عرض محتويات المجلدات
Options -Indexes

# إعدادات الأمان
<IfModule mod_headers.c>
    # منع تضمين الموقع في إطارات خارجية
    Header always append X-Frame-Options SAMEORIGIN
    
    # منع تخمين نوع المحتوى
    Header set X-Content-Type-Options nosniff
    
    # تفعيل حماية XSS
    Header set X-XSS-Protection "1; mode=block"
    
    # إخفاء معلومات الخادم
    Header unset Server
    Header unset X-Powered-By
</IfModule>

# ضغط الملفات لتحسين الأداء
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# إعدادات التخزين المؤقت
<IfModule mod_expires.c>
    ExpiresActive On
    
    # الصور
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    
    # CSS و JavaScript
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    
    # الخطوط
    ExpiresByType application/x-font-ttf "access plus 1 year"
    ExpiresByType font/opentype "access plus 1 year"
    ExpiresByType application/x-font-woff "access plus 1 year"
    ExpiresByType application/x-font-woff2 "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    
    # HTML
    ExpiresByType text/html "access plus 1 hour"
</IfModule>

# إعدادات رفع الملفات
<IfModule mod_php.c>
    php_value upload_max_filesize 10M
    php_value post_max_size 10M
    php_value max_execution_time 300
    php_value max_input_time 300
    php_value memory_limit 256M
</IfModule>

# إعادة توجيه الأخطاء
ErrorDocument 403 /ALarchef2/errors/403.php
ErrorDocument 404 /ALarchef2/errors/404.php
ErrorDocument 500 /ALarchef2/errors/500.php

# منع الوصول المباشر إلى ملفات PHP في مجلد uploads
<Directory "uploads">
    <FilesMatch "\.php$">
        Order Allow,Deny
        Deny from all
    </FilesMatch>
</Directory>

# إعدادات HTTPS (إذا كان متوفراً)
<IfModule mod_rewrite.c>
    # إعادة توجيه إلى HTTPS (اختياري)
    # RewriteCond %{HTTPS} off
    # RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
    
    # إزالة www (اختياري)
    # RewriteCond %{HTTP_HOST} ^www\.(.*)$ [NC]
    # RewriteRule ^(.*)$ http://%1/$1 [R=301,L]
</IfModule>

# منع الوصول إلى ملفات النظام المخفية
<FilesMatch "^\.">
    Order allow,deny
    Deny from all
</FilesMatch>

# حماية إضافية لملفات التكوين
<Files "config.php">
    Order Allow,Deny
    Deny from all
</Files>

<Files "database.php">
    Order Allow,Deny
    Deny from all
</Files>

# منع تنفيذ سكريبت في مجلد الرفع
<Directory "uploads">
    php_flag engine off
    AddHandler cgi-script .php .pl .py .jsp .asp .sh .cgi
    Options -ExecCGI
</Directory>

# إعدادات الجلسة
<IfModule mod_php.c>
    php_value session.cookie_httponly 1
    php_value session.cookie_secure 0
    php_value session.use_only_cookies 1
    php_value session.cookie_samesite "Strict"
</IfModule>

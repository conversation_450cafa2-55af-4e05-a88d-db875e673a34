# نظام أرشفة مصنع أسمنت البرح
## ALarchef Archive System

نظام أرشفة شامل لإدارة الموظفين والمستندات والمراسلات والرعاية الطبية في مصنع أسمنت البرح.

## المميزات الرئيسية

### 🏢 إدارة شئون الموظفين
- إدارة البيانات الشخصية والوظيفية للموظفين
- نظام الحركات الوظيفية (فردي/جماعي)
- تتبع الترقيات والنقل والإعارة
- حساب سنوات الخدمة والعمر
- تقارير التقاعد

### 📁 أرشفة المستندات
- رفع وتخزين المستندات الرقمية
- تصنيف المستندات حسب النوع والإدارة
- البحث المتقدم في المستندات
- دعم أنواع ملفات متعددة (PDF, DOC, XLS, صور)

### 📧 السكرتارية العامة
- إدارة المراسلات الصادرة والواردة
- محاضر الاجتماعات
- تصنيف المراسلات حسب الأولوية والجهة

### 🏥 الرعاية الطبية والتأمين
- تسجيل الزيارات الطبية
- إدارة التقارير الطبية
- متابعة العلاجات والأدوية
- إدارة وثائق التأمين

### 📊 التقارير والإحصائيات
- تقارير شاملة عن الموظفين
- إحصائيات الإدارات ونوع العمالة
- تقارير المستندات والمراسلات
- تصدير التقارير إلى Excel/PDF

## متطلبات النظام

### متطلبات الخادم
- **PHP**: 7.4 أو أحدث
- **MySQL**: 5.7 أو أحدث
- **Apache/Nginx**: مع mod_rewrite
- **Extensions المطلوبة**:
  - PDO MySQL
  - Mbstring
  - GD (للصور)
  - Zip (للنسخ الاحتياطي)

### متطلبات المتصفح
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## التثبيت والإعداد

### 1. تحميل الملفات
```bash
# نسخ الملفات إلى مجلد الخادم
cp -r ALarchef2 /path/to/xampp/htdocs/
```

### 2. إعداد قاعدة البيانات
1. تشغيل XAMPP
2. فتح phpMyAdmin
3. إنشاء قاعدة بيانات جديدة (اختياري - سيتم إنشاؤها تلقائياً)

### 3. تكوين النظام
1. تعديل ملف `config/database.php` إذا لزم الأمر
2. التأكد من إعدادات قاعدة البيانات:
   ```php
   define('DB_HOST', 'localhost');
   define('DB_PORT', '3306');
   define('DB_NAME', 'alarchef_archive');
   define('DB_USER', 'root');
   define('DB_PASS', '');
   ```

### 4. تشغيل التثبيت
1. فتح المتصفح والانتقال إلى: `http://localhost:8080/ALarchef2/install.php`
2. اتباع تعليمات التثبيت
3. انتظار اكتمال إنشاء قاعدة البيانات

### 5. تسجيل الدخول الأول
- **الرابط**: `http://localhost:8080/ALarchef2/login.php`
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`

⚠️ **مهم**: يرجى تغيير كلمة المرور الافتراضية فور تسجيل الدخول الأول.

## هيكل النظام

```
ALarchef2/
├── api/                    # ملفات API
├── assets/                 # الملفات الثابتة (CSS, JS, صور)
├── backups/               # النسخ الاحتياطية
├── config/                # ملفات التكوين
├── database/              # ملفات قاعدة البيانات
├── includes/              # الملفات المشتركة
├── logs/                  # ملفات السجلات
├── pages/                 # صفحات النظام
│   ├── employees/         # إدارة الموظفين
│   ├── documents/         # أرشفة المستندات
│   ├── correspondences/   # المراسلات
│   ├── meetings/          # محاضر الاجتماعات
│   ├── medical/           # الرعاية الطبية
│   ├── reports/           # التقارير
│   └── settings/          # الإعدادات
├── uploads/               # الملفات المرفوعة
├── index.php             # الصفحة الرئيسية
├── login.php             # صفحة تسجيل الدخول
└── install.php           # ملف التثبيت
```

## الأدوار والصلاحيات

### 1. مدير النظام (admin)
- جميع الصلاحيات
- إدارة المستخدمين
- إعدادات النظام
- النسخ الاحتياطي

### 2. موظف الأرشيف (archive_staff)
- إدارة الموظفين
- أرشفة المستندات
- الحركات الوظيفية
- المراسلات

### 3. موظف الرعاية الطبية (medical_staff)
- الزيارات الطبية
- التقارير الطبية
- العلاجات والأدوية
- وثائق التأمين

### 4. موظف عادي (employee)
- عرض البيانات فقط
- البحث والاستعلام

## الاستخدام

### إضافة موظف جديد
1. الانتقال إلى "شئون الموظفين" → "إضافة موظف جديد"
2. ملء البيانات الشخصية (مطلوبة)
3. ملء البيانات الوظيفية
4. حفظ البيانات

### رفع مستند
1. الانتقال إلى "أرشفة المستندات" → "رفع مستند جديد"
2. اختيار الموظف
3. تحديد نوع المستند
4. رفع الملف
5. حفظ المستند

### إنشاء حركة وظيفية
1. الانتقال إلى "الحركات الوظيفية" → "إضافة حركة جديدة"
2. اختيار نوع الحركة (فردي/جماعي)
3. ملء تفاصيل الحركة
4. إرفاق المستند (اختياري)
5. حفظ الحركة

## النسخ الاحتياطي

### إنشاء نسخة احتياطية
1. الانتقال إلى "إعدادات النظام" → "النسخ الاحتياطي"
2. النقر على "إنشاء نسخة احتياطية"
3. انتظار اكتمال العملية
4. تحميل الملف

### استعادة نسخة احتياطية
1. الانتقال إلى "إعدادات النظام" → "النسخ الاحتياطي"
2. اختيار ملف النسخة الاحتياطية
3. النقر على "استعادة"
4. تأكيد العملية

## استيراد البيانات من Excel

### تحضير ملف Excel
1. تحميل النموذج من النظام
2. ملء البيانات حسب التنسيق المطلوب
3. حفظ الملف بصيغة .xlsx أو .csv

### استيراد البيانات
1. الانتقال إلى "شئون الموظفين" → "استيراد من Excel"
2. رفع ملف Excel
3. مراجعة البيانات
4. تأكيد الاستيراد

## الأمان

### كلمات المرور
- يجب أن تحتوي على 8 أحرف على الأقل
- يُنصح بتضمين أرقام ورموز
- تغيير كلمة المرور دورياً

### رفع الملفات
- الحد الأقصى: 10 ميجابايت
- الأنواع المسموحة: PDF, DOC, DOCX, XLS, XLSX, JPG, PNG
- فحص الملفات تلقائياً

### الجلسات
- انتهاء تلقائي للجلسة (قابل للتخصيص)
- تسجيل جميع العمليات
- حماية من CSRF

## استكشاف الأخطاء

### مشاكل شائعة

#### خطأ في الاتصال بقاعدة البيانات
```
الحل: التحقق من إعدادات قاعدة البيانات في config/database.php
```

#### خطأ في رفع الملفات
```
الحل: التحقق من صلاحيات مجلد uploads/
chmod 755 uploads/
```

#### صفحة بيضاء
```
الحل: تفعيل عرض الأخطاء في PHP
ini_set('display_errors', 1);
```

### ملفات السجلات
- **مسار السجلات**: `logs/`
- **سجل الأخطاء**: `logs/error.log`
- **سجل النشاط**: `logs/activity.log`

## الدعم الفني

### معلومات النظام
- **الإصدار**: 1.0.0
- **تاريخ الإصدار**: 2024
- **المطور**: فريق تطوير نظام الأرشفة

### التواصل
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +967-xxx-xxxx

## الترخيص

هذا النظام مطور خصيصاً لمصنع أسمنت البرح. جميع الحقوق محفوظة.

---

**ملاحظة**: يرجى قراءة هذا الدليل بعناية قبل استخدام النظام. في حالة وجود أي استفسارات، يرجى التواصل مع فريق الدعم الفني.

<?php
/**
 * صفحة قائمة الحركات الوظيفية
 * Job Movements List Page
 */

require_once '../../config/database.php';
require_once '../../includes/auth.php';

// التحقق من تسجيل الدخول
requireLogin();

$auth = new Auth();
$currentUser = $auth->getCurrentUser();

// جلب الحركات الوظيفية
try {
    $db = getDB();
    
    // إعداد الاستعلام مع الفلترة
    $whereClause = "WHERE jm.is_active = 1";
    $params = [];
    
    // فلترة حسب نوع الحركة
    if (isset($_GET['movement_type']) && !empty($_GET['movement_type'])) {
        $whereClause .= " AND jmt.id = ?";
        $params[] = (int)$_GET['movement_type'];
    }
    
    // فلترة حسب التاريخ
    if (isset($_GET['date_from']) && !empty($_GET['date_from'])) {
        $whereClause .= " AND jm.movement_date >= ?";
        $params[] = $_GET['date_from'];
    }
    
    if (isset($_GET['date_to']) && !empty($_GET['date_to'])) {
        $whereClause .= " AND jm.movement_date <= ?";
        $params[] = $_GET['date_to'];
    }
    
    // البحث في العنوان
    if (isset($_GET['search']) && !empty($_GET['search'])) {
        $search = '%' . $_GET['search'] . '%';
        $whereClause .= " AND jm.movement_title LIKE ?";
        $params[] = $search;
    }
    
    $stmt = $db->prepare("
        SELECT 
            jm.id,
            jm.movement_title,
            jm.movement_date,
            jm.is_group_movement,
            jm.created_at,
            jmt.type_name,
            u.full_name as created_by_name,
            COUNT(jmd.id) as employees_count
        FROM job_movements jm
        JOIN job_movement_types jmt ON jm.movement_type_id = jmt.id
        JOIN users u ON jm.created_by = u.id
        LEFT JOIN job_movement_details jmd ON jm.id = jmd.movement_id
        $whereClause
        GROUP BY jm.id
        ORDER BY jm.movement_date DESC, jm.created_at DESC
    ");
    
    $stmt->execute($params);
    $movements = $stmt->fetchAll();
    
    // جلب أنواع الحركات للفلترة
    $stmt = $db->query("SELECT id, type_name FROM job_movement_types WHERE is_active = 1 ORDER BY type_name");
    $movementTypes = $stmt->fetchAll();
    
} catch (Exception $e) {
    logError("خطأ في جلب الحركات الوظيفية: " . $e->getMessage());
    $movements = [];
    $movementTypes = [];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قائمة الحركات الوظيفية - <?php echo SYSTEM_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/buttons/2.2.2/css/buttons.bootstrap5.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="../../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <?php include '../../includes/navbar.php'; ?>
    
    <!-- Sidebar -->
    <?php include '../../includes/sidebar.php'; ?>
    
    <!-- Main Content -->
    <div class="main-content">
        <div class="container-fluid">
            <!-- Page Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="page-header">
                        <h1 class="page-title">
                            <i class="bi bi-arrow-left-right me-2"></i>
                            قائمة الحركات الوظيفية
                        </h1>
                        <p class="page-subtitle">
                            إدارة وعرض الحركات الوظيفية للموظفين
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- Action Buttons -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex gap-2 flex-wrap">
                        <a href="add.php" class="btn btn-primary">
                            <i class="bi bi-plus-circle me-2"></i>
                            إضافة حركة جديدة
                        </a>
                        <button type="button" class="btn btn-success" onclick="exportToExcel()">
                            <i class="bi bi-file-earmark-excel me-2"></i>
                            تصدير إلى Excel
                        </button>
                        <button type="button" class="btn btn-info" onclick="printList()">
                            <i class="bi bi-printer me-2"></i>
                            طباعة
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Filters -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="bi bi-funnel me-2"></i>
                                فلترة النتائج
                            </h6>
                        </div>
                        <div class="card-body">
                            <form method="GET" class="row g-3">
                                <div class="col-md-3">
                                    <label for="movement_type" class="form-label">نوع الحركة</label>
                                    <select class="form-select" id="movement_type" name="movement_type">
                                        <option value="">جميع الأنواع</option>
                                        <?php foreach ($movementTypes as $type): ?>
                                            <option value="<?php echo $type['id']; ?>" 
                                                    <?php echo (isset($_GET['movement_type']) && $_GET['movement_type'] == $type['id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($type['type_name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="col-md-2">
                                    <label for="date_from" class="form-label">من تاريخ</label>
                                    <input type="date" class="form-control" id="date_from" name="date_from" 
                                           value="<?php echo htmlspecialchars($_GET['date_from'] ?? ''); ?>">
                                </div>
                                
                                <div class="col-md-2">
                                    <label for="date_to" class="form-label">إلى تاريخ</label>
                                    <input type="date" class="form-control" id="date_to" name="date_to" 
                                           value="<?php echo htmlspecialchars($_GET['date_to'] ?? ''); ?>">
                                </div>
                                
                                <div class="col-md-3">
                                    <label for="search" class="form-label">البحث</label>
                                    <input type="text" class="form-control" id="search" name="search" 
                                           placeholder="البحث في عنوان الحركة..." 
                                           value="<?php echo htmlspecialchars($_GET['search'] ?? ''); ?>">
                                </div>
                                
                                <div class="col-md-2">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="bi bi-search"></i>
                                            بحث
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Statistics -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card stat-card stat-card-primary">
                        <div class="card-body">
                            <div class="stat-icon">
                                <i class="bi bi-arrow-left-right"></i>
                            </div>
                            <div class="stat-content">
                                <h3 class="stat-number"><?php echo convertToArabicNumbers(count($movements)); ?></h3>
                                <p class="stat-label">إجمالي الحركات</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card stat-card stat-card-success">
                        <div class="card-body">
                            <div class="stat-icon">
                                <i class="bi bi-people-fill"></i>
                            </div>
                            <div class="stat-content">
                                <h3 class="stat-number">
                                    <?php 
                                    $groupCount = count(array_filter($movements, function($mov) {
                                        return $mov['is_group_movement'] == 1;
                                    }));
                                    echo convertToArabicNumbers($groupCount);
                                    ?>
                                </h3>
                                <p class="stat-label">حركات جماعية</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card stat-card stat-card-info">
                        <div class="card-body">
                            <div class="stat-icon">
                                <i class="bi bi-person-fill"></i>
                            </div>
                            <div class="stat-content">
                                <h3 class="stat-number">
                                    <?php 
                                    $individualCount = count($movements) - $groupCount;
                                    echo convertToArabicNumbers($individualCount);
                                    ?>
                                </h3>
                                <p class="stat-label">حركات فردية</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card stat-card stat-card-warning">
                        <div class="card-body">
                            <div class="stat-icon">
                                <i class="bi bi-calendar-month"></i>
                            </div>
                            <div class="stat-content">
                                <h3 class="stat-number">
                                    <?php 
                                    $thisMonthCount = count(array_filter($movements, function($mov) {
                                        return date('Y-m', strtotime($mov['movement_date'])) === date('Y-m');
                                    }));
                                    echo convertToArabicNumbers($thisMonthCount);
                                    ?>
                                </h3>
                                <p class="stat-label">هذا الشهر</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Movements Table -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-list me-2"></i>
                                الحركات الوظيفية
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table id="movementsTable" class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>عنوان الحركة</th>
                                            <th>نوع الحركة</th>
                                            <th>تاريخ الحركة</th>
                                            <th>نوع الإجراء</th>
                                            <th>عدد الموظفين</th>
                                            <th>أنشئ بواسطة</th>
                                            <th>تاريخ الإنشاء</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($movements as $movement): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($movement['movement_title']); ?></strong>
                                                </td>
                                                <td>
                                                    <span class="badge bg-primary">
                                                        <?php echo htmlspecialchars($movement['type_name']); ?>
                                                    </span>
                                                </td>
                                                <td><?php echo formatArabicDate($movement['movement_date']); ?></td>
                                                <td>
                                                    <?php if ($movement['is_group_movement']): ?>
                                                        <span class="badge bg-success">
                                                            <i class="bi bi-people me-1"></i>
                                                            جماعي
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="badge bg-info">
                                                            <i class="bi bi-person me-1"></i>
                                                            فردي
                                                        </span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <span class="badge bg-secondary">
                                                        <?php echo convertToArabicNumbers($movement['employees_count']); ?>
                                                    </span>
                                                </td>
                                                <td><?php echo htmlspecialchars($movement['created_by_name']); ?></td>
                                                <td><?php echo formatArabicDate($movement['created_at']); ?></td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <a href="view.php?id=<?php echo $movement['id']; ?>" 
                                                           class="btn btn-sm btn-outline-info" title="عرض">
                                                            <i class="bi bi-eye"></i>
                                                        </a>
                                                        <a href="edit.php?id=<?php echo $movement['id']; ?>" 
                                                           class="btn btn-sm btn-outline-primary" title="تعديل">
                                                            <i class="bi bi-pencil"></i>
                                                        </a>
                                                        <a href="print.php?id=<?php echo $movement['id']; ?>" 
                                                           class="btn btn-sm btn-outline-secondary" title="طباعة" target="_blank">
                                                            <i class="bi bi-printer"></i>
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.bootstrap5.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.html5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.print.min.js"></script>
    <script src="../../assets/js/main.js"></script>
    
    <script>
        // تهيئة DataTable
        $(document).ready(function() {
            $('#movementsTable').DataTable({
                language: {
                    url: 'https://cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json'
                },
                responsive: true,
                pageLength: 25,
                lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, 'الكل']],
                dom: 'Bfrtip',
                buttons: [
                    {
                        extend: 'excel',
                        text: 'تصدير Excel',
                        className: 'btn btn-success btn-sm'
                    },
                    {
                        extend: 'pdf',
                        text: 'تصدير PDF',
                        className: 'btn btn-danger btn-sm'
                    },
                    {
                        extend: 'print',
                        text: 'طباعة',
                        className: 'btn btn-info btn-sm'
                    }
                ],
                order: [[2, 'desc']] // ترتيب حسب تاريخ الحركة
            });
        });
        
        function exportToExcel() {
            $('#movementsTable').DataTable().button('.buttons-excel').trigger();
        }
        
        function printList() {
            $('#movementsTable').DataTable().button('.buttons-print').trigger();
        }
    </script>
</body>
</html>

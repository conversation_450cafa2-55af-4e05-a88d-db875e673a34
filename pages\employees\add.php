<?php
/**
 * صفحة إضافة موظف جديد
 * Add New Employee Page
 */

require_once '../../config/database.php';
require_once '../../includes/auth.php';

// التحقق من تسجيل الدخول
requireLogin();

$auth = new Auth();
$currentUser = $auth->getCurrentUser();

$message = '';
$messageType = 'info';

// معالجة إرسال النموذج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db = getDB();
        $db->beginTransaction();
        
        // التحقق من صحة البيانات
        $errors = [];
        
        // البيانات الشخصية
        $employeeNumber = sanitize($_POST['employee_number']);
        $fullName = sanitize($_POST['full_name']);
        $birthDate = $_POST['birth_date'];
        $nationalId = formatNationalId(sanitize($_POST['national_id']));
        $bloodType = sanitize($_POST['blood_type']);
        $birthPlace = sanitize($_POST['birth_place']);
        $residenceAddress = sanitize($_POST['residence_address']);
        $phoneNumber = sanitize($_POST['phone_number']);
        $gender = $_POST['gender'];
        
        // البيانات الوظيفية
        $jobNumber = sanitize($_POST['job_number']);
        $appointmentDate = $_POST['appointment_date'];
        $employmentTypeId = (int)$_POST['employment_type_id'];
        $departmentId = (int)$_POST['department_id'];
        $circleId = !empty($_POST['circle_id']) ? (int)$_POST['circle_id'] : null;
        $sectionId = !empty($_POST['section_id']) ? (int)$_POST['section_id'] : null;
        $jobTitleId = !empty($_POST['job_title_id']) ? (int)$_POST['job_title_id'] : null;
        $jobPosition = sanitize($_POST['job_position']);
        $levelNumber = !empty($_POST['level_number']) ? (int)$_POST['level_number'] : null;
        $gradeNumber = !empty($_POST['grade_number']) ? (int)$_POST['grade_number'] : null;
        $workplace = sanitize($_POST['workplace']);
        
        // التحقق من صحة البيانات
        if (empty($employeeNumber)) {
            $errors[] = 'رقم الموظف مطلوب';
        }
        
        if (empty($fullName)) {
            $errors[] = 'اسم الموظف مطلوب';
        }
        
        if (empty($birthDate)) {
            $errors[] = 'تاريخ الميلاد مطلوب';
        }
        
        if (!validateNationalId($nationalId)) {
            $errors[] = 'الرقم الوطني يجب أن يكون 11 رقم';
        }
        
        if (!empty($jobNumber) && !validateJobNumber($jobNumber)) {
            $errors[] = 'الرقم الوظيفي يجب أن يكون 7 أرقام';
        }
        
        if (empty($appointmentDate)) {
            $errors[] = 'تاريخ التعيين مطلوب';
        }
        
        if ($employmentTypeId <= 0) {
            $errors[] = 'نوع العمالة مطلوب';
        }
        
        if ($departmentId <= 0) {
            $errors[] = 'الإدارة مطلوبة';
        }
        
        // التحقق من عدم تكرار رقم الموظف
        $stmt = $db->prepare("SELECT id FROM employees_personal WHERE employee_number = ?");
        $stmt->execute([$employeeNumber]);
        if ($stmt->fetch()) {
            $errors[] = 'رقم الموظف موجود بالفعل';
        }
        
        // التحقق من عدم تكرار الرقم الوطني
        $stmt = $db->prepare("SELECT id FROM employees_personal WHERE national_id = ?");
        $stmt->execute([$nationalId]);
        if ($stmt->fetch()) {
            $errors[] = 'الرقم الوطني موجود بالفعل';
        }
        
        // التحقق من عدم تكرار الرقم الوظيفي
        if (!empty($jobNumber)) {
            $stmt = $db->prepare("SELECT id FROM employees_job WHERE job_number = ?");
            $stmt->execute([$jobNumber]);
            if ($stmt->fetch()) {
                $errors[] = 'الرقم الوظيفي موجود بالفعل';
            }
        }
        
        if (empty($errors)) {
            // إدراج البيانات الشخصية
            $stmt = $db->prepare("
                INSERT INTO employees_personal 
                (employee_number, full_name, birth_date, national_id, blood_type, 
                 birth_place, residence_address, phone_number, gender) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $employeeNumber, $fullName, $birthDate, $nationalId, $bloodType,
                $birthPlace, $residenceAddress, $phoneNumber, $gender
            ]);
            
            $employeeId = $db->lastInsertId();
            
            // إدراج البيانات الوظيفية
            $stmt = $db->prepare("
                INSERT INTO employees_job 
                (employee_id, job_number, appointment_date, employment_type_id, 
                 department_id, circle_id, section_id, job_title_id, job_position, 
                 level_number, grade_number, workplace) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $employeeId, $jobNumber, $appointmentDate, $employmentTypeId,
                $departmentId, $circleId, $sectionId, $jobTitleId, $jobPosition,
                $levelNumber, $gradeNumber, $workplace
            ]);
            
            $db->commit();
            
            $message = 'تم إضافة الموظف بنجاح';
            $messageType = 'success';
            
            // إعادة توجيه إلى صفحة عرض الموظف
            header("Location: view.php?id=$employeeId&message=" . urlencode($message));
            exit;
            
        } else {
            $db->rollBack();
            $message = implode('<br>', $errors);
            $messageType = 'danger';
        }
        
    } catch (Exception $e) {
        $db->rollBack();
        logError("خطأ في إضافة الموظف: " . $e->getMessage());
        $message = 'حدث خطأ في إضافة الموظف';
        $messageType = 'danger';
    }
}

// جلب البيانات المرجعية
try {
    $db = getDB();
    
    // جلب أنواع العمالة
    $stmt = $db->query("SELECT id, type_name FROM employment_types WHERE is_active = 1 ORDER BY type_order");
    $employmentTypes = $stmt->fetchAll();
    
    // جلب الإدارات
    $stmt = $db->query("SELECT id, dept_name FROM departments WHERE is_active = 1 ORDER BY dept_name");
    $departments = $stmt->fetchAll();
    
    // جلب المجموعات الوظيفية
    $stmt = $db->query("SELECT id, group_name FROM job_groups WHERE is_active = 1 ORDER BY group_number");
    $jobGroups = $stmt->fetchAll();
    
} catch (Exception $e) {
    logError("خطأ في جلب البيانات المرجعية: " . $e->getMessage());
    $employmentTypes = [];
    $departments = [];
    $jobGroups = [];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة موظف جديد - <?php echo SYSTEM_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="../../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <?php include '../../includes/navbar.php'; ?>
    
    <!-- Sidebar -->
    <?php include '../../includes/sidebar.php'; ?>
    
    <!-- Main Content -->
    <div class="main-content">
        <div class="container-fluid">
            <!-- Page Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="page-header">
                        <h1 class="page-title">
                            <i class="bi bi-person-plus me-2"></i>
                            إضافة موظف جديد
                        </h1>
                        <p class="page-subtitle">
                            إدخال البيانات الشخصية والوظيفية للموظف الجديد
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- Messages -->
            <?php if (!empty($message)): ?>
                <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                    <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <!-- Employee Form -->
            <form method="POST" class="needs-validation" novalidate>
                <div class="row">
                    <!-- البيانات الشخصية -->
                    <div class="col-lg-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-person me-2"></i>
                                    البيانات الشخصية
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="employee_number" class="form-label">رقم الموظف *</label>
                                        <input type="text" class="form-control" id="employee_number" 
                                               name="employee_number" required 
                                               value="<?php echo htmlspecialchars($_POST['employee_number'] ?? ''); ?>">
                                        <div class="invalid-feedback">يرجى إدخال رقم الموظف</div>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="national_id" class="form-label">الرقم الوطني *</label>
                                        <input type="text" class="form-control" id="national_id" 
                                               name="national_id" required maxlength="11" 
                                               value="<?php echo htmlspecialchars($_POST['national_id'] ?? ''); ?>">
                                        <div class="form-text">11 رقم (سيتم إضافة أصفار على اليسار إذا لزم الأمر)</div>
                                        <div class="invalid-feedback">يرجى إدخال الرقم الوطني</div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="full_name" class="form-label">الاسم الكامل *</label>
                                    <input type="text" class="form-control" id="full_name" 
                                           name="full_name" required 
                                           value="<?php echo htmlspecialchars($_POST['full_name'] ?? ''); ?>">
                                    <div class="invalid-feedback">يرجى إدخال الاسم الكامل</div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="birth_date" class="form-label">تاريخ الميلاد *</label>
                                        <input type="date" class="form-control" id="birth_date" 
                                               name="birth_date" required 
                                               value="<?php echo htmlspecialchars($_POST['birth_date'] ?? ''); ?>">
                                        <div class="invalid-feedback">يرجى إدخال تاريخ الميلاد</div>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="gender" class="form-label">الجنس *</label>
                                        <select class="form-select" id="gender" name="gender" required>
                                            <option value="">اختر الجنس</option>
                                            <option value="ذكر" <?php echo (($_POST['gender'] ?? '') === 'ذكر') ? 'selected' : ''; ?>>ذكر</option>
                                            <option value="انثى" <?php echo (($_POST['gender'] ?? '') === 'انثى') ? 'selected' : ''; ?>>أنثى</option>
                                        </select>
                                        <div class="invalid-feedback">يرجى اختيار الجنس</div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="blood_type" class="form-label">فصيلة الدم</label>
                                        <select class="form-select" id="blood_type" name="blood_type">
                                            <option value="">اختر فصيلة الدم</option>
                                            <option value="A+" <?php echo (($_POST['blood_type'] ?? '') === 'A+') ? 'selected' : ''; ?>>A+</option>
                                            <option value="A-" <?php echo (($_POST['blood_type'] ?? '') === 'A-') ? 'selected' : ''; ?>>A-</option>
                                            <option value="B+" <?php echo (($_POST['blood_type'] ?? '') === 'B+') ? 'selected' : ''; ?>>B+</option>
                                            <option value="B-" <?php echo (($_POST['blood_type'] ?? '') === 'B-') ? 'selected' : ''; ?>>B-</option>
                                            <option value="AB+" <?php echo (($_POST['blood_type'] ?? '') === 'AB+') ? 'selected' : ''; ?>>AB+</option>
                                            <option value="AB-" <?php echo (($_POST['blood_type'] ?? '') === 'AB-') ? 'selected' : ''; ?>>AB-</option>
                                            <option value="O+" <?php echo (($_POST['blood_type'] ?? '') === 'O+') ? 'selected' : ''; ?>>O+</option>
                                            <option value="O-" <?php echo (($_POST['blood_type'] ?? '') === 'O-') ? 'selected' : ''; ?>>O-</option>
                                        </select>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="phone_number" class="form-label">رقم الهاتف</label>
                                        <input type="tel" class="form-control" id="phone_number" 
                                               name="phone_number" 
                                               value="<?php echo htmlspecialchars($_POST['phone_number'] ?? ''); ?>">
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="birth_place" class="form-label">مكان الميلاد</label>
                                    <input type="text" class="form-control" id="birth_place" 
                                           name="birth_place" 
                                           value="<?php echo htmlspecialchars($_POST['birth_place'] ?? ''); ?>">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="residence_address" class="form-label">مقر الإقامة</label>
                                    <textarea class="form-control" id="residence_address" 
                                              name="residence_address" rows="3"><?php echo htmlspecialchars($_POST['residence_address'] ?? ''); ?></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- البيانات الوظيفية -->
                    <div class="col-lg-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-briefcase me-2"></i>
                                    البيانات الوظيفية
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="job_number" class="form-label">الرقم الوظيفي</label>
                                        <input type="text" class="form-control" id="job_number" 
                                               name="job_number" maxlength="7" 
                                               value="<?php echo htmlspecialchars($_POST['job_number'] ?? ''); ?>">
                                        <div class="form-text">7 أرقام</div>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="appointment_date" class="form-label">تاريخ التعيين *</label>
                                        <input type="date" class="form-control" id="appointment_date" 
                                               name="appointment_date" required 
                                               value="<?php echo htmlspecialchars($_POST['appointment_date'] ?? ''); ?>">
                                        <div class="invalid-feedback">يرجى إدخال تاريخ التعيين</div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="employment_type_id" class="form-label">نوع العمالة *</label>
                                    <select class="form-select" id="employment_type_id" name="employment_type_id" required>
                                        <option value="">اختر نوع العمالة</option>
                                        <?php foreach ($employmentTypes as $type): ?>
                                            <option value="<?php echo $type['id']; ?>" 
                                                    <?php echo (($_POST['employment_type_id'] ?? '') == $type['id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($type['type_name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="invalid-feedback">يرجى اختيار نوع العمالة</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="department_id" class="form-label">الإدارة *</label>
                                    <select class="form-select" id="department_id" name="department_id" required>
                                        <option value="">اختر الإدارة</option>
                                        <?php foreach ($departments as $dept): ?>
                                            <option value="<?php echo $dept['id']; ?>" 
                                                    <?php echo (($_POST['department_id'] ?? '') == $dept['id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($dept['dept_name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="invalid-feedback">يرجى اختيار الإدارة</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="circle_id" class="form-label">الدائرة</label>
                                    <select class="form-select" id="circle_id" name="circle_id">
                                        <option value="">اختر الدائرة</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="section_id" class="form-label">القسم</label>
                                    <select class="form-select" id="section_id" name="section_id">
                                        <option value="">اختر القسم</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="job_title_id" class="form-label">المسمى الوظيفي</label>
                                    <select class="form-select" id="job_title_id" name="job_title_id">
                                        <option value="">اختر المسمى الوظيفي</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="job_position" class="form-label">الوظيفة</label>
                                    <input type="text" class="form-control" id="job_position" 
                                           name="job_position" 
                                           value="<?php echo htmlspecialchars($_POST['job_position'] ?? ''); ?>">
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="level_number" class="form-label">المستوى (0-6)</label>
                                        <input type="number" class="form-control" id="level_number" 
                                               name="level_number" min="0" max="6" 
                                               value="<?php echo htmlspecialchars($_POST['level_number'] ?? ''); ?>">
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="grade_number" class="form-label">الدرجة (1-20)</label>
                                        <input type="number" class="form-control" id="grade_number" 
                                               name="grade_number" min="1" max="20" 
                                               value="<?php echo htmlspecialchars($_POST['grade_number'] ?? ''); ?>">
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="workplace" class="form-label">مقر العمل</label>
                                    <input type="text" class="form-control" id="workplace" 
                                           name="workplace" 
                                           value="<?php echo htmlspecialchars($_POST['workplace'] ?? ''); ?>">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body">
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-save me-2"></i>
                                        حفظ البيانات
                                    </button>
                                    <a href="list.php" class="btn btn-secondary">
                                        <i class="bi bi-arrow-right me-2"></i>
                                        العودة إلى القائمة
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../../assets/js/main.js"></script>
    
    <script>
        // تحديث الدوائر عند تغيير الإدارة
        document.getElementById('department_id').addEventListener('change', function() {
            const departmentId = this.value;
            const circleSelect = document.getElementById('circle_id');
            const sectionSelect = document.getElementById('section_id');
            
            // مسح الدوائر والأقسام
            circleSelect.innerHTML = '<option value="">اختر الدائرة</option>';
            sectionSelect.innerHTML = '<option value="">اختر القسم</option>';
            
            if (departmentId) {
                fetch(`../../api/get_circles.php?department_id=${departmentId}`)
                    .then(response => response.json())
                    .then(circles => {
                        circles.forEach(circle => {
                            const option = document.createElement('option');
                            option.value = circle.id;
                            option.textContent = circle.circle_name;
                            circleSelect.appendChild(option);
                        });
                    })
                    .catch(error => console.error('خطأ في جلب الدوائر:', error));
            }
        });
        
        // تحديث الأقسام عند تغيير الدائرة
        document.getElementById('circle_id').addEventListener('change', function() {
            const circleId = this.value;
            const sectionSelect = document.getElementById('section_id');
            
            // مسح الأقسام
            sectionSelect.innerHTML = '<option value="">اختر القسم</option>';
            
            if (circleId) {
                fetch(`../../api/get_sections.php?circle_id=${circleId}`)
                    .then(response => response.json())
                    .then(sections => {
                        sections.forEach(section => {
                            const option = document.createElement('option');
                            option.value = section.id;
                            option.textContent = section.section_name;
                            sectionSelect.appendChild(option);
                        });
                    })
                    .catch(error => console.error('خطأ في جلب الأقسام:', error));
            }
        });
        
        // تحديث المستوى تلقائياً عند تغيير الدرجة
        document.getElementById('grade_number').addEventListener('change', function() {
            const grade = parseInt(this.value);
            const levelInput = document.getElementById('level_number');
            
            if (grade >= 1 && grade <= 20) {
                // حساب المستوى بناءً على الدرجة
                let level;
                if (grade >= 1 && grade <= 3) level = 0;
                else if (grade >= 4 && grade <= 6) level = 1;
                else if (grade >= 7 && grade <= 9) level = 2;
                else if (grade >= 10 && grade <= 12) level = 3;
                else if (grade >= 13 && grade <= 15) level = 4;
                else if (grade >= 16 && grade <= 18) level = 5;
                else if (grade >= 19 && grade <= 20) level = 6;
                
                levelInput.value = level;
            }
        });
        
        // تنسيق الرقم الوطني
        document.getElementById('national_id').addEventListener('input', function() {
            // إزالة أي شيء غير الأرقام
            this.value = this.value.replace(/\D/g, '');
            
            // تحديد الحد الأقصى 11 رقم
            if (this.value.length > 11) {
                this.value = this.value.substring(0, 11);
            }
        });
        
        // تنسيق الرقم الوظيفي
        document.getElementById('job_number').addEventListener('input', function() {
            // إزالة أي شيء غير الأرقام
            this.value = this.value.replace(/\D/g, '');
            
            // تحديد الحد الأقصى 7 أرقام
            if (this.value.length > 7) {
                this.value = this.value.substring(0, 7);
            }
        });
    </script>
</body>
</html>

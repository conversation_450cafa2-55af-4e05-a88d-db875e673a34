<?php
/**
 * إعداد سريع للنظام
 * Quick System Setup
 */

// إعدادات قاعدة البيانات
$host = 'localhost';
$port = '3306';
$dbname = 'alarchef_archive';
$username = 'root';
$password = '';

echo "<h1>إعداد سريع لنظام أرشفة مصنع أسمنت البرح</h1>";

try {
    // الاتصال بـ MySQL
    $pdo = new PDO("mysql:host=$host;port=$port;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ تم الاتصال بـ MySQL<br>";
    
    // إنشاء قاعدة البيانات
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "✅ تم إنشاء قاعدة البيانات<br>";
    
    // الاتصال بقاعدة البيانات
    $pdo->exec("USE `$dbname`");
    
    // إنشاء جدول المستخدمين
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            full_name VARCHAR(100) NOT NULL,
            email VARCHAR(100),
            role ENUM('admin', 'archive_staff', 'medical_staff', 'employee') DEFAULT 'employee',
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    echo "✅ تم إنشاء جدول المستخدمين<br>";
    
    // إنشاء مستخدم المدير
    $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
    $stmt = $pdo->prepare("INSERT IGNORE INTO users (username, password, full_name, email, role) VALUES (?, ?, ?, ?, ?)");
    $stmt->execute(['admin', $adminPassword, 'مدير النظام', '<EMAIL>', 'admin']);
    echo "✅ تم إنشاء مستخدم المدير<br>";
    
    // إنشاء جدول الدوائر
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS circles (
            id INT AUTO_INCREMENT PRIMARY KEY,
            circle_name VARCHAR(100) NOT NULL,
            circle_description TEXT,
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ");
    
    // إنشاء جدول الإدارات
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS departments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            dept_name VARCHAR(100) NOT NULL,
            dept_description TEXT,
            circle_id INT,
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (circle_id) REFERENCES circles(id)
        )
    ");
    
    // إنشاء جدول أنواع العمالة
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS employment_types (
            id INT AUTO_INCREMENT PRIMARY KEY,
            type_name VARCHAR(50) NOT NULL,
            type_description TEXT,
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ");
    
    // إنشاء جدول الموظفين
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS employees_personal (
            id INT AUTO_INCREMENT PRIMARY KEY,
            employee_number VARCHAR(20) UNIQUE NOT NULL,
            full_name VARCHAR(100) NOT NULL,
            national_id VARCHAR(20) UNIQUE NOT NULL,
            birth_date DATE,
            birth_place VARCHAR(100),
            gender ENUM('ذكر', 'انثى') NOT NULL,
            marital_status ENUM('أعزب', 'متزوج', 'مطلق', 'أرمل') DEFAULT 'أعزب',
            phone VARCHAR(20),
            email VARCHAR(100),
            address TEXT,
            emergency_contact_name VARCHAR(100),
            emergency_contact_phone VARCHAR(20),
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    
    // إنشاء جدول البيانات الوظيفية
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS employees_job (
            id INT AUTO_INCREMENT PRIMARY KEY,
            employee_id INT NOT NULL,
            job_title VARCHAR(100),
            department_id INT,
            employment_type_id INT,
            appointment_date DATE,
            salary DECIMAL(10,2),
            job_grade VARCHAR(20),
            direct_manager VARCHAR(100),
            work_location VARCHAR(100),
            contract_end_date DATE,
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (employee_id) REFERENCES employees_personal(id),
            FOREIGN KEY (department_id) REFERENCES departments(id),
            FOREIGN KEY (employment_type_id) REFERENCES employment_types(id)
        )
    ");
    
    // إنشاء جدول أنواع المستندات
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS document_types (
            id INT AUTO_INCREMENT PRIMARY KEY,
            type_name VARCHAR(100) NOT NULL,
            type_description TEXT,
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ");
    
    // إنشاء جدول المستندات
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS documents (
            id INT AUTO_INCREMENT PRIMARY KEY,
            employee_id INT NOT NULL,
            document_type_id INT NOT NULL,
            document_title VARCHAR(200) NOT NULL,
            document_description TEXT,
            file_name VARCHAR(255) NOT NULL,
            file_path VARCHAR(500) NOT NULL,
            file_type VARCHAR(10) NOT NULL,
            file_size INT NOT NULL,
            upload_date DATE NOT NULL,
            uploaded_by INT NOT NULL,
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (employee_id) REFERENCES employees_personal(id),
            FOREIGN KEY (document_type_id) REFERENCES document_types(id),
            FOREIGN KEY (uploaded_by) REFERENCES users(id)
        )
    ");
    
    // إنشاء جدول المراسلات
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS correspondences (
            id INT AUTO_INCREMENT PRIMARY KEY,
            correspondence_number VARCHAR(50) UNIQUE NOT NULL,
            correspondence_type ENUM('صادرة', 'واردة') NOT NULL,
            subject VARCHAR(200) NOT NULL,
            from_entity VARCHAR(200),
            to_entity VARCHAR(200),
            correspondence_date DATE NOT NULL,
            received_date DATE,
            classification VARCHAR(100),
            priority ENUM('عادي', 'مهم', 'عاجل', 'سري') DEFAULT 'عادي',
            content TEXT,
            file_attachment VARCHAR(500),
            created_by INT NOT NULL,
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (created_by) REFERENCES users(id)
        )
    ");
    
    // إنشاء جدول الزيارات الطبية
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS medical_visits (
            id INT AUTO_INCREMENT PRIMARY KEY,
            employee_id INT NOT NULL,
            visit_date DATE NOT NULL,
            visit_type ENUM('فحص دوري', 'مرض', 'إصابة عمل', 'متابعة', 'طوارئ') NOT NULL,
            symptoms TEXT NOT NULL,
            diagnosis TEXT,
            treatment TEXT,
            medications TEXT,
            doctor_name VARCHAR(100),
            clinic_hospital VARCHAR(200),
            follow_up_required TINYINT(1) DEFAULT 0,
            follow_up_date DATE,
            medical_report VARCHAR(500),
            notes TEXT,
            created_by INT NOT NULL,
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (employee_id) REFERENCES employees_personal(id),
            FOREIGN KEY (created_by) REFERENCES users(id)
        )
    ");
    
    echo "✅ تم إنشاء جميع الجداول الأساسية<br>";
    
    // إدراج بيانات أساسية
    $pdo->exec("INSERT IGNORE INTO circles (circle_name) VALUES ('دائرة الإنتاج'), ('دائرة الإدارة والمالية'), ('دائرة الصيانة والهندسة'), ('دائرة الجودة والمختبرات')");
    
    $pdo->exec("INSERT IGNORE INTO departments (dept_name, circle_id) VALUES 
        ('الإدارة العامة', 2), 
        ('الموارد البشرية', 2), 
        ('المالية والمحاسبة', 2), 
        ('إدارة الإنتاج', 1), 
        ('إدارة الجودة', 4), 
        ('إدارة الصيانة', 3)");
    
    $pdo->exec("INSERT IGNORE INTO employment_types (type_name) VALUES ('رسمي'), ('متعاقد'), ('مؤقت'), ('متقاعد')");
    
    $pdo->exec("INSERT IGNORE INTO document_types (type_name) VALUES 
        ('السيرة الذاتية'), 
        ('الشهادات العلمية'), 
        ('شهادات الخبرة'), 
        ('صورة الهوية'), 
        ('صورة شخصية'), 
        ('عقد العمل'), 
        ('التأمينات الاجتماعية'), 
        ('الفحص الطبي')");
    
    echo "✅ تم إدراج البيانات الأساسية<br>";
    
    echo "<br><h2>🎉 تم إعداد النظام بنجاح!</h2>";
    echo "<p><strong>بيانات تسجيل الدخول:</strong></p>";
    echo "<ul>";
    echo "<li><strong>اسم المستخدم:</strong> admin</li>";
    echo "<li><strong>كلمة المرور:</strong> admin123</li>";
    echo "</ul>";
    
    echo "<p><a href='login.php' style='background: #0d6efd; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>الذهاب لصفحة تسجيل الدخول</a></p>";
    
} catch (PDOException $e) {
    echo "❌ خطأ: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد سريع للنظام</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        h1, h2 { color: #333; }
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
</body>
</html>

<?php
/**
 * ملف تثبيت النظام
 * System Installation File
 */

// التحقق من وجود ملف التكوين
if (!file_exists('config/database.php')) {
    die('ملف التكوين غير موجود. يرجى التأكد من وجود ملف config/database.php');
}

require_once 'config/database.php';

$message = '';
$messageType = 'info';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // قراءة ملف SQL
        $sqlFile = 'database/alarchef_database.sql';
        if (!file_exists($sqlFile)) {
            throw new Exception('ملف قاعدة البيانات غير موجود');
        }
        
        $sql = file_get_contents($sqlFile);
        
        // الاتصال بـ MySQL بدون تحديد قاعدة بيانات
        $dsn = "mysql:host=" . DB_HOST . ";port=" . DB_PORT . ";charset=" . DB_CHARSET;
        $pdo = new PDO($dsn, DB_USER, DB_PASS, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]);
        
        // تنفيذ الاستعلامات
        $statements = explode(';', $sql);
        $executedCount = 0;
        
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement) && !preg_match('/^--/', $statement)) {
                $pdo->exec($statement);
                $executedCount++;
            }
        }
        
        $message = "تم تثبيت النظام بنجاح! تم تنفيذ $executedCount استعلام.";
        $messageType = 'success';
        
        // إنشاء المجلدات المطلوبة
        $directories = [
            'uploads',
            'uploads/documents',
            'uploads/correspondences',
            'uploads/meetings',
            'uploads/medical',
            'backups',
            'logs'
        ];
        
        foreach ($directories as $dir) {
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }
        }
        
        $message .= '<br>تم إنشاء المجلدات المطلوبة.';
        
    } catch (Exception $e) {
        $message = 'خطأ في التثبيت: ' . $e->getMessage();
        $messageType = 'danger';
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت النظام - <?php echo SYSTEM_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .install-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }
        
        .install-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            max-width: 600px;
            width: 100%;
        }
        
        .install-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .install-body {
            padding: 2rem;
        }
        
        .system-logo {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 2rem;
        }
        
        .requirements-list {
            list-style: none;
            padding: 0;
        }
        
        .requirements-list li {
            padding: 0.5rem 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .requirements-list li:last-child {
            border-bottom: none;
        }
        
        .status-icon {
            width: 20px;
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="install-container">
        <div class="install-card">
            <div class="install-header">
                <div class="system-logo">
                    <i class="bi bi-gear-fill"></i>
                </div>
                <h2>تثبيت النظام</h2>
                <p>نظام أرشفة مصنع أسمنت البرح</p>
            </div>
            
            <div class="install-body">
                <?php if (!empty($message)): ?>
                    <div class="alert alert-<?php echo $messageType; ?>" role="alert">
                        <?php echo $message; ?>
                    </div>
                    
                    <?php if ($messageType === 'success'): ?>
                        <div class="text-center mt-4">
                            <a href="login.php" class="btn btn-primary btn-lg">
                                <i class="bi bi-box-arrow-in-right me-2"></i>
                                الانتقال إلى تسجيل الدخول
                            </a>
                        </div>
                        
                        <div class="mt-4">
                            <h6>بيانات تسجيل الدخول الافتراضية:</h6>
                            <ul class="list-unstyled">
                                <li><strong>اسم المستخدم:</strong> admin</li>
                                <li><strong>كلمة المرور:</strong> admin123</li>
                            </ul>
                            <small class="text-muted">يرجى تغيير كلمة المرور بعد تسجيل الدخول</small>
                        </div>
                    <?php endif; ?>
                <?php else: ?>
                    <h5 class="mb-4">متطلبات النظام</h5>
                    
                    <ul class="requirements-list">
                        <li>
                            <span class="status-icon">
                                <?php if (version_compare(PHP_VERSION, '7.4.0', '>=')): ?>
                                    <i class="bi bi-check-circle-fill text-success"></i>
                                <?php else: ?>
                                    <i class="bi bi-x-circle-fill text-danger"></i>
                                <?php endif; ?>
                            </span>
                            PHP 7.4 أو أحدث (الحالي: <?php echo PHP_VERSION; ?>)
                        </li>
                        
                        <li>
                            <span class="status-icon">
                                <?php if (extension_loaded('pdo_mysql')): ?>
                                    <i class="bi bi-check-circle-fill text-success"></i>
                                <?php else: ?>
                                    <i class="bi bi-x-circle-fill text-danger"></i>
                                <?php endif; ?>
                            </span>
                            PDO MySQL Extension
                        </li>
                        
                        <li>
                            <span class="status-icon">
                                <?php if (extension_loaded('mbstring')): ?>
                                    <i class="bi bi-check-circle-fill text-success"></i>
                                <?php else: ?>
                                    <i class="bi bi-x-circle-fill text-danger"></i>
                                <?php endif; ?>
                            </span>
                            Mbstring Extension
                        </li>
                        
                        <li>
                            <span class="status-icon">
                                <?php if (is_writable('.')): ?>
                                    <i class="bi bi-check-circle-fill text-success"></i>
                                <?php else: ?>
                                    <i class="bi bi-x-circle-fill text-danger"></i>
                                <?php endif; ?>
                            </span>
                            صلاحيات الكتابة في المجلد الجذر
                        </li>
                        
                        <li>
                            <span class="status-icon">
                                <?php 
                                try {
                                    $dsn = "mysql:host=" . DB_HOST . ";port=" . DB_PORT;
                                    $pdo = new PDO($dsn, DB_USER, DB_PASS);
                                    echo '<i class="bi bi-check-circle-fill text-success"></i>';
                                    $dbConnection = true;
                                } catch (Exception $e) {
                                    echo '<i class="bi bi-x-circle-fill text-danger"></i>';
                                    $dbConnection = false;
                                }
                                ?>
                            </span>
                            الاتصال بقاعدة البيانات MySQL
                        </li>
                    </ul>
                    
                    <?php 
                    $canInstall = version_compare(PHP_VERSION, '7.4.0', '>=') && 
                                  extension_loaded('pdo_mysql') && 
                                  extension_loaded('mbstring') && 
                                  is_writable('.') && 
                                  $dbConnection;
                    ?>
                    
                    <?php if ($canInstall): ?>
                        <div class="alert alert-success mt-4">
                            <i class="bi bi-check-circle-fill me-2"></i>
                            جميع المتطلبات متوفرة. يمكنك المتابعة مع التثبيت.
                        </div>
                        
                        <form method="POST" class="mt-4">
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="bi bi-download me-2"></i>
                                    بدء التثبيت
                                </button>
                            </div>
                        </form>
                    <?php else: ?>
                        <div class="alert alert-danger mt-4">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                            يرجى التأكد من توفر جميع المتطلبات قبل المتابعة.
                        </div>
                    <?php endif; ?>
                    
                    <div class="mt-4">
                        <h6>ملاحظات مهمة:</h6>
                        <ul class="small text-muted">
                            <li>تأكد من إعداد بيانات الاتصال بقاعدة البيانات في ملف config/database.php</li>
                            <li>سيتم إنشاء قاعدة البيانات والجداول تلقائياً</li>
                            <li>سيتم إنشاء حساب مدير افتراضي (admin/admin123)</li>
                            <li>يرجى تغيير كلمة المرور الافتراضية بعد التثبيت</li>
                        </ul>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

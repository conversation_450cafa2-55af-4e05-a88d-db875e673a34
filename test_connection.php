<?php
/**
 * اختبار الاتصال بقاعدة البيانات
 * Database Connection Test
 */

// إعدادات قاعدة البيانات
$host = 'localhost';
$port = '3306';
$dbname = 'alarchef_archive';
$username = 'root';
$password = '';

echo "<h2>اختبار الاتصال بقاعدة البيانات</h2>";

try {
    // محاولة الاتصال بـ MySQL
    $pdo = new PDO("mysql:host=$host;port=$port;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ تم الاتصال بـ MySQL بنجاح<br>";
    
    // التحقق من وجود قاعدة البيانات
    $stmt = $pdo->query("SHOW DATABASES LIKE '$dbname'");
    if ($stmt->rowCount() > 0) {
        echo "✅ قاعدة البيانات '$dbname' موجودة<br>";
        
        // الاتصال بقاعدة البيانات
        $pdo->exec("USE `$dbname`");
        
        // التحقق من وجود الجداول
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (count($tables) > 0) {
            echo "✅ تم العثور على " . count($tables) . " جدول<br>";
            echo "<ul>";
            foreach ($tables as $table) {
                echo "<li>$table</li>";
            }
            echo "</ul>";
            
            // التحقق من وجود مستخدم المدير
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM users WHERE role = 'admin'");
            $adminCount = $stmt->fetch()['count'];
            
            if ($adminCount > 0) {
                echo "✅ يوجد $adminCount مدير في النظام<br>";
            } else {
                echo "⚠️ لا يوجد مديرين في النظام<br>";
            }
            
        } else {
            echo "⚠️ لا توجد جداول في قاعدة البيانات<br>";
            echo "<a href='install/setup_database.php'>إنشاء الجداول</a><br>";
        }
        
    } else {
        echo "❌ قاعدة البيانات '$dbname' غير موجودة<br>";
        echo "<a href='install/setup_database.php'>إنشاء قاعدة البيانات</a><br>";
    }
    
} catch (PDOException $e) {
    echo "❌ خطأ في الاتصال: " . $e->getMessage() . "<br>";
    
    if (strpos($e->getMessage(), 'Access denied') !== false) {
        echo "<p>تأكد من:</p>";
        echo "<ul>";
        echo "<li>تشغيل خادم MySQL</li>";
        echo "<li>صحة اسم المستخدم وكلمة المرور</li>";
        echo "<li>صلاحيات المستخدم</li>";
        echo "</ul>";
    } elseif (strpos($e->getMessage(), 'Connection refused') !== false) {
        echo "<p>تأكد من:</p>";
        echo "<ul>";
        echo "<li>تشغيل خادم MySQL</li>";
        echo "<li>صحة عنوان الخادم والمنفذ</li>";
        echo "</ul>";
    }
}

echo "<br><hr>";
echo "<h3>معلومات النظام:</h3>";
echo "<ul>";
echo "<li><strong>إصدار PHP:</strong> " . phpversion() . "</li>";
echo "<li><strong>إصدار MySQL:</strong> ";
try {
    $stmt = $pdo->query("SELECT VERSION() as version");
    echo $stmt->fetch()['version'];
} catch (Exception $e) {
    echo "غير متاح";
}
echo "</li>";
echo "<li><strong>مجلد العمل:</strong> " . __DIR__ . "</li>";
echo "<li><strong>الوقت الحالي:</strong> " . date('Y-m-d H:i:s') . "</li>";
echo "</ul>";

echo "<br>";
echo "<a href='login.php'>الذهاب لصفحة تسجيل الدخول</a> | ";
echo "<a href='install/setup_database.php'>إعداد قاعدة البيانات</a>";
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الاتصال</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; padding: 2rem; }
        .container { max-width: 800px; }
    </style>
</head>
<body>
    <div class="container">
        <!-- النتائج ستظهر هنا -->
    </div>
</body>
</html>

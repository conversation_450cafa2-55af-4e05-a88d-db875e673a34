<?php
/**
 * إعداد قاعدة البيانات - نظام أرشفة مصنع أسمنت البرح
 * Database Setup Script
 */

// إعدادات قاعدة البيانات
$host = 'localhost';
$port = '3306';
$dbname = 'alarchef_archive';
$username = 'root';
$password = '';

try {
    // الاتصال بـ MySQL بدون تحديد قاعدة بيانات
    $pdo = new PDO("mysql:host=$host;port=$port;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ تم الاتصال بـ MySQL بنجاح<br>";
    
    // إنشاء قاعدة البيانات
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "✅ تم إنشاء قاعدة البيانات: $dbname<br>";
    
    // الاتصال بقاعدة البيانات المحددة
    $pdo->exec("USE `$dbname`");
    
    // إنشاء الجداول
    $tables = [
        // جدول المستخدمين
        "CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            full_name VARCHAR(100) NOT NULL,
            email VARCHAR(100),
            role ENUM('admin', 'archive_staff', 'medical_staff', 'employee') DEFAULT 'employee',
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )",
        
        // جدول الدوائر
        "CREATE TABLE IF NOT EXISTS circles (
            id INT AUTO_INCREMENT PRIMARY KEY,
            circle_name VARCHAR(100) NOT NULL,
            circle_description TEXT,
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )",
        
        // جدول الإدارات
        "CREATE TABLE IF NOT EXISTS departments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            dept_name VARCHAR(100) NOT NULL,
            dept_description TEXT,
            circle_id INT,
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (circle_id) REFERENCES circles(id)
        )",
        
        // جدول أنواع العمالة
        "CREATE TABLE IF NOT EXISTS employment_types (
            id INT AUTO_INCREMENT PRIMARY KEY,
            type_name VARCHAR(50) NOT NULL,
            type_description TEXT,
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )",
        
        // جدول البيانات الشخصية للموظفين
        "CREATE TABLE IF NOT EXISTS employees_personal (
            id INT AUTO_INCREMENT PRIMARY KEY,
            employee_number VARCHAR(20) UNIQUE NOT NULL,
            full_name VARCHAR(100) NOT NULL,
            national_id VARCHAR(20) UNIQUE NOT NULL,
            birth_date DATE,
            birth_place VARCHAR(100),
            gender ENUM('ذكر', 'انثى') NOT NULL,
            marital_status ENUM('أعزب', 'متزوج', 'مطلق', 'أرمل') DEFAULT 'أعزب',
            phone VARCHAR(20),
            email VARCHAR(100),
            address TEXT,
            emergency_contact_name VARCHAR(100),
            emergency_contact_phone VARCHAR(20),
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )",
        
        // جدول البيانات الوظيفية للموظفين
        "CREATE TABLE IF NOT EXISTS employees_job (
            id INT AUTO_INCREMENT PRIMARY KEY,
            employee_id INT NOT NULL,
            job_title VARCHAR(100),
            department_id INT,
            employment_type_id INT,
            appointment_date DATE,
            salary DECIMAL(10,2),
            job_grade VARCHAR(20),
            direct_manager VARCHAR(100),
            work_location VARCHAR(100),
            contract_end_date DATE,
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (employee_id) REFERENCES employees_personal(id),
            FOREIGN KEY (department_id) REFERENCES departments(id),
            FOREIGN KEY (employment_type_id) REFERENCES employment_types(id)
        )",
        
        // جدول أنواع المستندات
        "CREATE TABLE IF NOT EXISTS document_types (
            id INT AUTO_INCREMENT PRIMARY KEY,
            type_name VARCHAR(100) NOT NULL,
            type_description TEXT,
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )",
        
        // جدول المستندات
        "CREATE TABLE IF NOT EXISTS documents (
            id INT AUTO_INCREMENT PRIMARY KEY,
            employee_id INT NOT NULL,
            document_type_id INT NOT NULL,
            document_title VARCHAR(200) NOT NULL,
            document_description TEXT,
            file_name VARCHAR(255) NOT NULL,
            file_path VARCHAR(500) NOT NULL,
            file_type VARCHAR(10) NOT NULL,
            file_size INT NOT NULL,
            upload_date DATE NOT NULL,
            uploaded_by INT NOT NULL,
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (employee_id) REFERENCES employees_personal(id),
            FOREIGN KEY (document_type_id) REFERENCES document_types(id),
            FOREIGN KEY (uploaded_by) REFERENCES users(id)
        )",
        
        // جدول المراسلات
        "CREATE TABLE IF NOT EXISTS correspondences (
            id INT AUTO_INCREMENT PRIMARY KEY,
            correspondence_number VARCHAR(50) UNIQUE NOT NULL,
            correspondence_type ENUM('صادرة', 'واردة') NOT NULL,
            subject VARCHAR(200) NOT NULL,
            from_entity VARCHAR(200),
            to_entity VARCHAR(200),
            correspondence_date DATE NOT NULL,
            received_date DATE,
            classification VARCHAR(100),
            priority ENUM('عادي', 'مهم', 'عاجل', 'سري') DEFAULT 'عادي',
            content TEXT,
            file_attachment VARCHAR(500),
            created_by INT NOT NULL,
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (created_by) REFERENCES users(id)
        )",
        
        // جدول الزيارات الطبية
        "CREATE TABLE IF NOT EXISTS medical_visits (
            id INT AUTO_INCREMENT PRIMARY KEY,
            employee_id INT NOT NULL,
            visit_date DATE NOT NULL,
            visit_type ENUM('فحص دوري', 'مرض', 'إصابة عمل', 'متابعة', 'طوارئ') NOT NULL,
            symptoms TEXT NOT NULL,
            diagnosis TEXT,
            treatment TEXT,
            medications TEXT,
            doctor_name VARCHAR(100),
            clinic_hospital VARCHAR(200),
            follow_up_required TINYINT(1) DEFAULT 0,
            follow_up_date DATE,
            medical_report VARCHAR(500),
            notes TEXT,
            created_by INT NOT NULL,
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (employee_id) REFERENCES employees_personal(id),
            FOREIGN KEY (created_by) REFERENCES users(id)
        )",
        
        // جدول الحركات الوظيفية
        "CREATE TABLE IF NOT EXISTS job_movements (
            id INT AUTO_INCREMENT PRIMARY KEY,
            movement_type ENUM('فردي', 'جماعي') NOT NULL,
            movement_category ENUM('نقل', 'ترقية', 'تسوية', 'تعديل راتب', 'إنهاء خدمة') NOT NULL,
            employee_id INT,
            employees_data JSON,
            from_department_id INT,
            to_department_id INT,
            from_job_title VARCHAR(100),
            to_job_title VARCHAR(100),
            from_salary DECIMAL(10,2),
            to_salary DECIMAL(10,2),
            movement_date DATE NOT NULL,
            decision_number VARCHAR(100),
            decision_date DATE,
            notes TEXT,
            created_by INT NOT NULL,
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (employee_id) REFERENCES employees_personal(id),
            FOREIGN KEY (from_department_id) REFERENCES departments(id),
            FOREIGN KEY (to_department_id) REFERENCES departments(id),
            FOREIGN KEY (created_by) REFERENCES users(id)
        )",
        
        // جدول النسخ الاحتياطية
        "CREATE TABLE IF NOT EXISTS backups (
            id INT AUTO_INCREMENT PRIMARY KEY,
            backup_name VARCHAR(255) NOT NULL,
            backup_path VARCHAR(500) NOT NULL,
            backup_size BIGINT NOT NULL,
            created_by INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (created_by) REFERENCES users(id)
        )",
        
        // جدول إعدادات النظام
        "CREATE TABLE IF NOT EXISTS system_settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            setting_key VARCHAR(100) UNIQUE NOT NULL,
            setting_value TEXT,
            updated_by INT,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (updated_by) REFERENCES users(id)
        )"
    ];
    
    foreach ($tables as $table) {
        $pdo->exec($table);
        echo "✅ تم إنشاء جدول بنجاح<br>";
    }
    
    // إدراج البيانات الأساسية
    
    // إنشاء مستخدم المدير الافتراضي
    $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
    $pdo->exec("INSERT IGNORE INTO users (username, password, full_name, email, role) 
               VALUES ('admin', '$adminPassword', 'مدير النظام', '<EMAIL>', 'admin')");
    echo "✅ تم إنشاء مستخدم المدير الافتراضي (admin/admin123)<br>";
    
    // إدراج الدوائر الأساسية
    $circles = [
        "دائرة الإنتاج",
        "دائرة الإدارة والمالية", 
        "دائرة الصيانة والهندسة",
        "دائرة الجودة والمختبرات"
    ];
    
    foreach ($circles as $circle) {
        $pdo->exec("INSERT IGNORE INTO circles (circle_name) VALUES ('$circle')");
    }
    echo "✅ تم إدراج الدوائر الأساسية<br>";
    
    // إدراج الإدارات الأساسية
    $departments = [
        ['الإدارة العامة', 2],
        ['الموارد البشرية', 2],
        ['المالية والمحاسبة', 2],
        ['إدارة الإنتاج', 1],
        ['إدارة الجودة', 4],
        ['إدارة الصيانة', 3],
        ['الأمن والسلامة', 2]
    ];
    
    foreach ($departments as $dept) {
        $pdo->exec("INSERT IGNORE INTO departments (dept_name, circle_id) VALUES ('{$dept[0]}', {$dept[1]})");
    }
    echo "✅ تم إدراج الإدارات الأساسية<br>";
    
    // إدراج أنواع العمالة
    $employmentTypes = [
        'رسمي',
        'متعاقد', 
        'مؤقت',
        'متقاعد'
    ];
    
    foreach ($employmentTypes as $type) {
        $pdo->exec("INSERT IGNORE INTO employment_types (type_name) VALUES ('$type')");
    }
    echo "✅ تم إدراج أنواع العمالة<br>";
    
    // إدراج أنواع المستندات
    $documentTypes = [
        'السيرة الذاتية',
        'الشهادات العلمية',
        'شهادات الخبرة',
        'صورة الهوية',
        'صورة شخصية',
        'عقد العمل',
        'التأمينات الاجتماعية',
        'الفحص الطبي',
        'شهادة حسن السيرة والسلوك'
    ];
    
    foreach ($documentTypes as $type) {
        $pdo->exec("INSERT IGNORE INTO document_types (type_name) VALUES ('$type')");
    }
    echo "✅ تم إدراج أنواع المستندات<br>";
    
    echo "<br><h3>🎉 تم إعداد قاعدة البيانات بنجاح!</h3>";
    echo "<p>يمكنك الآن الدخول للنظام باستخدام:</p>";
    echo "<ul>";
    echo "<li><strong>اسم المستخدم:</strong> admin</li>";
    echo "<li><strong>كلمة المرور:</strong> admin123</li>";
    echo "</ul>";
    echo "<p><a href='../login.php' class='btn btn-primary'>الذهاب لصفحة تسجيل الدخول</a></p>";
    
} catch (PDOException $e) {
    echo "❌ خطأ في قاعدة البيانات: " . $e->getMessage();
} catch (Exception $e) {
    echo "❌ خطأ عام: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد قاعدة البيانات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .container { max-width: 800px; margin-top: 2rem; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-4">إعداد قاعدة البيانات - نظام أرشفة مصنع أسمنت البرح</h1>
        <div class="card">
            <div class="card-body">
                <!-- النتائج ستظهر هنا -->
            </div>
        </div>
    </div>
</body>
</html>

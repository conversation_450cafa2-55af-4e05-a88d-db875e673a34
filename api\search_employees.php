<?php
/**
 * API البحث عن الموظفين
 * Employee Search API
 */

require_once '../config/database.php';
require_once '../includes/auth.php';

// التحقق من تسجيل الدخول
requireLogin();

header('Content-Type: application/json; charset=utf-8');

try {
    $db = getDB();
    
    $searchTerm = $_GET['q'] ?? '';
    $page = (int)($_GET['page'] ?? 1);
    $limit = 30;
    $offset = ($page - 1) * $limit;
    
    // إعداد الاستعلام
    $whereClause = "WHERE ep.is_active = 1";
    $params = [];
    
    if (!empty($searchTerm)) {
        $searchTerm = '%' . $searchTerm . '%';
        $whereClause .= " AND (ep.full_name LIKE ? OR ep.employee_number LIKE ? OR ej.job_number LIKE ?)";
        $params = [$searchTerm, $searchTerm, $searchTerm];
    }
    
    // جلب إجمالي العدد
    $countStmt = $db->prepare("
        SELECT COUNT(DISTINCT ep.id) as total
        FROM employees_personal ep
        LEFT JOIN employees_job ej ON ep.id = ej.employee_id
        $whereClause
    ");
    $countStmt->execute($params);
    $totalCount = $countStmt->fetch()['total'];
    
    // جلب البيانات
    $stmt = $db->prepare("
        SELECT DISTINCT
            ep.id,
            ep.employee_number,
            ep.full_name,
            ej.job_number,
            d.dept_name
        FROM employees_personal ep
        LEFT JOIN employees_job ej ON ep.id = ej.employee_id
        LEFT JOIN departments d ON ej.department_id = d.id
        $whereClause
        ORDER BY ep.full_name
        LIMIT ? OFFSET ?
    ");
    
    $params[] = $limit;
    $params[] = $offset;
    $stmt->execute($params);
    $employees = $stmt->fetchAll();
    
    // تنسيق النتائج لـ Select2
    $results = [];
    foreach ($employees as $employee) {
        $text = $employee['full_name'];
        if (!empty($employee['employee_number'])) {
            $text .= ' (' . $employee['employee_number'] . ')';
        }
        if (!empty($employee['dept_name'])) {
            $text .= ' - ' . $employee['dept_name'];
        }
        
        $results[] = [
            'id' => $employee['id'],
            'text' => $text
        ];
    }
    
    sendJsonResponse([
        'items' => $results,
        'total_count' => $totalCount
    ]);
    
} catch (Exception $e) {
    logError("خطأ في البحث عن الموظفين: " . $e->getMessage());
    sendJsonResponse(['error' => 'خطأ في الخادم'], 500);
}
?>

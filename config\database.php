<?php
/**
 * إعدادات قاعدة البيانات - نظام أرشفة مصنع أسمنت البرح
 * Database Configuration - ALarchef Archive System
 */

// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_PORT', '3306');
define('DB_NAME', 'alarchef_archive');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// إعدادات النظام
define('SYSTEM_NAME', 'نظام أرشفة مصنع أسمنت البرح');
define('SYSTEM_VERSION', '1.0.0');
define('BASE_URL', 'http://localhost:8080/ALarchef2/');
define('UPLOAD_PATH', 'uploads/');
define('BACKUP_PATH', 'backups/');

// إعدادات الأمان
define('SESSION_TIMEOUT', 0); // 0 = بلا حدود
define('MAX_FILE_SIZE', 10485760); // 10MB
define('ALLOWED_FILE_TYPES', ['pdf', 'jpg', 'jpeg', 'png', 'doc', 'docx', 'xls', 'xlsx', 'csv']);

// إعدادات التاريخ والوقت
date_default_timezone_set('Asia/Aden');

class Database {
    private static $instance = null;
    private $connection;
    
    private function __construct() {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";port=" . DB_PORT . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
            ];
            
            $this->connection = new PDO($dsn, DB_USER, DB_PASS, $options);
        } catch (PDOException $e) {
            die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
        }
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function getConnection() {
        return $this->connection;
    }
    
    // منع النسخ
    private function __clone() {}
    
    // منع إلغاء التسلسل
    public function __wakeup() {
        throw new Exception("Cannot unserialize singleton");
    }
}

// دالة للحصول على اتصال قاعدة البيانات
function getDB() {
    return Database::getInstance()->getConnection();
}

// دالة لتنظيف البيانات
function sanitize($data) {
    return htmlspecialchars(strip_tags(trim($data)), ENT_QUOTES, 'UTF-8');
}

// دالة لتشفير كلمة المرور
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

// دالة للتحقق من كلمة المرور
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

// دالة لإنشاء رمز عشوائي
function generateToken($length = 32) {
    return bin2hex(random_bytes($length));
}

// دالة لتحويل التاريخ إلى التنسيق العربي
function formatArabicDate($date) {
    if (empty($date)) return '';
    
    $timestamp = strtotime($date);
    $day = date('d', $timestamp);
    $month = date('m', $timestamp);
    $year = date('Y', $timestamp);
    
    $arabicMonths = [
        '01' => 'يناير', '02' => 'فبراير', '03' => 'مارس',
        '04' => 'أبريل', '05' => 'مايو', '06' => 'يونيو',
        '07' => 'يوليو', '08' => 'أغسطس', '09' => 'سبتمبر',
        '10' => 'أكتوبر', '11' => 'نوفمبر', '12' => 'ديسمبر'
    ];
    
    return $day . ' ' . $arabicMonths[$month] . ' ' . $year;
}

// دالة لتحويل الأرقام إلى العربية
function convertToArabicNumbers($string) {
    $western = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    $arabic = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return str_replace($western, $arabic, $string);
}

// دالة لحساب العمر
function calculateAge($birthDate) {
    if (empty($birthDate)) return 0;
    
    $birth = new DateTime($birthDate);
    $today = new DateTime();
    $age = $today->diff($birth);
    
    return $age->y;
}

// دالة لحساب سنوات الخدمة
function calculateServiceYears($appointmentDate) {
    if (empty($appointmentDate)) return 0;
    
    $appointment = new DateTime($appointmentDate);
    $today = new DateTime();
    $service = $today->diff($appointment);
    
    return $service->y;
}

// دالة للتحقق من صحة الرقم الوطني
function validateNationalId($nationalId) {
    // يجب أن يكون 11 رقم
    if (strlen($nationalId) !== 11) {
        return false;
    }
    
    // يجب أن يحتوي على أرقام فقط
    if (!ctype_digit($nationalId)) {
        return false;
    }
    
    return true;
}

// دالة لتنسيق الرقم الوطني
function formatNationalId($nationalId) {
    // إضافة أصفار على اليسار إذا كان أقل من 11 رقم
    return str_pad($nationalId, 11, '0', STR_PAD_LEFT);
}

// دالة للتحقق من صحة الرقم الوظيفي
function validateJobNumber($jobNumber) {
    // يجب أن يكون 7 أرقام
    if (strlen($jobNumber) !== 7) {
        return false;
    }
    
    // يجب أن يحتوي على أرقام فقط
    if (!ctype_digit($jobNumber)) {
        return false;
    }
    
    return true;
}

// دالة لتحديد نوع الملف
function getFileType($filename) {
    return strtolower(pathinfo($filename, PATHINFO_EXTENSION));
}

// دالة للتحقق من نوع الملف المسموح
function isAllowedFileType($filename) {
    $fileType = getFileType($filename);
    return in_array($fileType, ALLOWED_FILE_TYPES);
}

// دالة لتحويل حجم الملف إلى تنسيق قابل للقراءة
function formatFileSize($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}

// دالة لإنشاء مجلد إذا لم يكن موجود
function createDirectoryIfNotExists($path) {
    if (!is_dir($path)) {
        mkdir($path, 0755, true);
    }
}

// دالة لتسجيل الأخطاء
function logError($message, $file = 'error.log') {
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] $message" . PHP_EOL;
    file_put_contents("logs/$file", $logMessage, FILE_APPEND | LOCK_EX);
}

// دالة لإرسال استجابة JSON
function sendJsonResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit;
}

// إنشاء المجلدات المطلوبة
createDirectoryIfNotExists(UPLOAD_PATH);
createDirectoryIfNotExists(BACKUP_PATH);
createDirectoryIfNotExists('logs');

?>

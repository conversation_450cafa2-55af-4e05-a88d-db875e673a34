<?php
/**
 * استيراد قاعدة البيانات من ملف SQL
 * Import Database from SQL File
 */

// إعدادات قاعدة البيانات
$host = 'localhost';
$port = '3306';
$username = 'root';
$password = '';

echo "<h1>استيراد قاعدة البيانات</h1>";

try {
    // قراءة ملف SQL
    $sqlFile = 'database/alarchef_database.sql';
    
    if (!file_exists($sqlFile)) {
        throw new Exception("ملف قاعدة البيانات غير موجود: $sqlFile");
    }
    
    $sql = file_get_contents($sqlFile);
    echo "✅ تم قراءة ملف قاعدة البيانات<br>";
    
    // الاتصال بـ MySQL
    $pdo = new PDO("mysql:host=$host;port=$port;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ تم الاتصال بـ MySQL<br>";
    
    // تقسيم الاستعلامات
    $queries = explode(';', $sql);
    $executedQueries = 0;
    
    foreach ($queries as $query) {
        $query = trim($query);
        
        // تجاهل التعليقات والاستعلامات الفارغة
        if (empty($query) || strpos($query, '--') === 0) {
            continue;
        }
        
        try {
            $pdo->exec($query);
            $executedQueries++;
        } catch (PDOException $e) {
            // تجاهل أخطاء "already exists" و "duplicate entry"
            if (strpos($e->getMessage(), 'already exists') === false && 
                strpos($e->getMessage(), 'Duplicate entry') === false) {
                echo "⚠️ تحذير في الاستعلام: " . substr($query, 0, 50) . "... - " . $e->getMessage() . "<br>";
            }
        }
    }
    
    echo "✅ تم تنفيذ $executedQueries استعلام<br>";
    
    // التحقق من وجود قاعدة البيانات والجداول
    $pdo->exec("USE alarchef_archive");
    
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "✅ تم العثور على " . count($tables) . " جدول:<br>";
    echo "<ul>";
    foreach ($tables as $table) {
        echo "<li>$table</li>";
    }
    echo "</ul>";
    
    // التحقق من وجود المستخدمين
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    $userCount = $stmt->fetch()['count'];
    echo "✅ يوجد $userCount مستخدم في النظام<br>";
    
    // إنشاء مستخدم مدير إذا لم يكن موجود
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users WHERE role = 'admin'");
    $adminCount = $stmt->fetch()['count'];
    
    if ($adminCount == 0) {
        $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT INTO users (username, password, full_name, email, role) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute(['admin', $adminPassword, 'مدير النظام', '<EMAIL>', 'admin']);
        echo "✅ تم إنشاء مستخدم المدير<br>";
    } else {
        echo "✅ يوجد $adminCount مدير في النظام<br>";
    }
    
    echo "<br><h2>🎉 تم استيراد قاعدة البيانات بنجاح!</h2>";
    echo "<p><strong>بيانات تسجيل الدخول:</strong></p>";
    echo "<ul>";
    echo "<li><strong>اسم المستخدم:</strong> admin</li>";
    echo "<li><strong>كلمة المرور:</strong> admin123</li>";
    echo "</ul>";
    
    echo "<p><a href='login.php' style='background: #0d6efd; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>الذهاب لصفحة تسجيل الدخول</a></p>";
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "<br>";
    
    // اقتراحات لحل المشكلة
    echo "<h3>اقتراحات لحل المشكلة:</h3>";
    echo "<ul>";
    echo "<li>تأكد من تشغيل خادم MySQL</li>";
    echo "<li>تأكد من صحة بيانات الاتصال</li>";
    echo "<li>تأكد من وجود ملف قاعدة البيانات في المسار الصحيح</li>";
    echo "</ul>";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>استيراد قاعدة البيانات</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        h1, h2 { color: #333; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
    </style>
</head>
<body>
</body>
</html>

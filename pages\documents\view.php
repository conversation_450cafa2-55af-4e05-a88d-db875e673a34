<?php
/**
 * صفحة عرض المستند
 * View Document Page
 */

require_once '../../config/database.php';
require_once '../../includes/auth.php';

// التحقق من تسجيل الدخول
requireLogin();

$auth = new Auth();
$currentUser = $auth->getCurrentUser();

$documentId = (int)($_GET['id'] ?? 0);
$message = $_GET['message'] ?? '';

if ($documentId <= 0) {
    header('Location: list.php');
    exit;
}

// جلب بيانات المستند
try {
    $db = getDB();
    
    $stmt = $db->prepare("
        SELECT 
            d.*,
            ep.full_name as employee_name,
            ep.employee_number,
            ep.national_id,
            dt.type_name as document_type,
            dept.dept_name,
            u.full_name as uploaded_by_name
        FROM documents d
        JOIN employees_personal ep ON d.employee_id = ep.id
        JOIN document_types dt ON d.document_type_id = dt.id
        LEFT JOIN employees_job ej ON ep.id = ej.employee_id
        LEFT JOIN departments dept ON ej.department_id = dept.id
        JOIN users u ON d.uploaded_by = u.id
        WHERE d.id = ? AND d.is_active = 1
    ");
    
    $stmt->execute([$documentId]);
    $document = $stmt->fetch();
    
    if (!$document) {
        header('Location: list.php?error=' . urlencode('المستند غير موجود'));
        exit;
    }
    
} catch (Exception $e) {
    logError("خطأ في جلب بيانات المستند: " . $e->getMessage());
    header('Location: list.php?error=' . urlencode('حدث خطأ في جلب بيانات المستند'));
    exit;
}

// تحديد نوع الملف لعرضه
$fileExtension = strtolower($document['file_type']);
$isImage = in_array($fileExtension, ['jpg', 'jpeg', 'png', 'gif']);
$isPdf = $fileExtension === 'pdf';
$isOfficeDoc = in_array($fileExtension, ['doc', 'docx', 'xls', 'xlsx']);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض المستند - <?php echo htmlspecialchars($document['document_title']); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="../../assets/css/style.css" rel="stylesheet">
    
    <style>
        .document-viewer {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 2rem;
            min-height: 500px;
        }
        
        .document-preview {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .file-icon {
            font-size: 8rem;
            color: #6c757d;
        }
        
        .document-info {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .info-item:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: 600;
            color: #495057;
        }
        
        .info-value {
            color: #6c757d;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <?php include '../../includes/navbar.php'; ?>
    
    <!-- Sidebar -->
    <?php include '../../includes/sidebar.php'; ?>
    
    <!-- Main Content -->
    <div class="main-content">
        <div class="container-fluid">
            <!-- Page Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="page-header">
                        <h1 class="page-title">
                            <i class="bi bi-file-earmark-text me-2"></i>
                            عرض المستند
                        </h1>
                        <p class="page-subtitle">
                            <?php echo htmlspecialchars($document['document_title']); ?>
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- Messages -->
            <?php if (!empty($message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <!-- Action Buttons -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex gap-2 flex-wrap">
                        <a href="../../<?php echo $document['file_path']; ?>" 
                           class="btn btn-primary" target="_blank">
                            <i class="bi bi-download me-2"></i>
                            تحميل الملف
                        </a>
                        <a href="edit.php?id=<?php echo $document['id']; ?>" 
                           class="btn btn-outline-primary">
                            <i class="bi bi-pencil me-2"></i>
                            تعديل البيانات
                        </a>
                        <button type="button" class="btn btn-outline-info" onclick="printDocument()">
                            <i class="bi bi-printer me-2"></i>
                            طباعة
                        </button>
                        <a href="list.php" class="btn btn-secondary">
                            <i class="bi bi-arrow-right me-2"></i>
                            العودة إلى القائمة
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <!-- معلومات المستند -->
                <div class="col-lg-4 mb-4">
                    <div class="document-info">
                        <h5 class="mb-3">
                            <i class="bi bi-info-circle me-2"></i>
                            معلومات المستند
                        </h5>
                        
                        <div class="info-item">
                            <span class="info-label">العنوان:</span>
                            <span class="info-value"><?php echo htmlspecialchars($document['document_title']); ?></span>
                        </div>
                        
                        <div class="info-item">
                            <span class="info-label">النوع:</span>
                            <span class="badge bg-primary"><?php echo htmlspecialchars($document['document_type']); ?></span>
                        </div>
                        
                        <div class="info-item">
                            <span class="info-label">اسم الملف:</span>
                            <span class="info-value"><?php echo htmlspecialchars($document['file_name']); ?></span>
                        </div>
                        
                        <div class="info-item">
                            <span class="info-label">نوع الملف:</span>
                            <span class="badge bg-secondary"><?php echo strtoupper($document['file_type']); ?></span>
                        </div>
                        
                        <div class="info-item">
                            <span class="info-label">حجم الملف:</span>
                            <span class="info-value"><?php echo formatFileSize($document['file_size']); ?></span>
                        </div>
                        
                        <div class="info-item">
                            <span class="info-label">تاريخ الرفع:</span>
                            <span class="info-value"><?php echo formatArabicDate($document['upload_date']); ?></span>
                        </div>
                        
                        <div class="info-item">
                            <span class="info-label">رفع بواسطة:</span>
                            <span class="info-value"><?php echo htmlspecialchars($document['uploaded_by_name']); ?></span>
                        </div>
                        
                        <?php if (!empty($document['document_description'])): ?>
                            <div class="mt-3">
                                <h6>الوصف:</h6>
                                <p class="text-muted"><?php echo nl2br(htmlspecialchars($document['document_description'])); ?></p>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- معلومات الموظف -->
                    <div class="document-info mt-4">
                        <h5 class="mb-3">
                            <i class="bi bi-person me-2"></i>
                            معلومات الموظف
                        </h5>
                        
                        <div class="info-item">
                            <span class="info-label">الاسم:</span>
                            <span class="info-value"><?php echo htmlspecialchars($document['employee_name']); ?></span>
                        </div>
                        
                        <div class="info-item">
                            <span class="info-label">رقم الموظف:</span>
                            <span class="info-value"><?php echo htmlspecialchars($document['employee_number']); ?></span>
                        </div>
                        
                        <div class="info-item">
                            <span class="info-label">الرقم الوطني:</span>
                            <span class="info-value"><?php echo htmlspecialchars($document['national_id']); ?></span>
                        </div>
                        
                        <?php if (!empty($document['dept_name'])): ?>
                            <div class="info-item">
                                <span class="info-label">الإدارة:</span>
                                <span class="info-value"><?php echo htmlspecialchars($document['dept_name']); ?></span>
                            </div>
                        <?php endif; ?>
                        
                        <div class="mt-3">
                            <a href="../employees/view.php?id=<?php echo $document['employee_id']; ?>" 
                               class="btn btn-sm btn-outline-primary">
                                <i class="bi bi-person-lines-fill me-1"></i>
                                عرض ملف الموظف
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- عارض المستند -->
                <div class="col-lg-8 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-eye me-2"></i>
                                معاينة المستند
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="document-viewer text-center">
                                <?php if ($isImage): ?>
                                    <!-- عرض الصور -->
                                    <img src="../../<?php echo $document['file_path']; ?>" 
                                         alt="<?php echo htmlspecialchars($document['document_title']); ?>"
                                         class="document-preview">
                                         
                                <?php elseif ($isPdf): ?>
                                    <!-- عرض PDF -->
                                    <iframe src="../../<?php echo $document['file_path']; ?>" 
                                            width="100%" height="600" 
                                            style="border: none; border-radius: 8px;">
                                        <p>متصفحك لا يدعم عرض ملفات PDF. 
                                           <a href="../../<?php echo $document['file_path']; ?>" target="_blank">انقر هنا لتحميل الملف</a>
                                        </p>
                                    </iframe>
                                    
                                <?php elseif ($isOfficeDoc): ?>
                                    <!-- عرض مستندات Office -->
                                    <div class="py-5">
                                        <i class="bi bi-file-earmark-<?php 
                                            echo in_array($fileExtension, ['doc', 'docx']) ? 'word' : 'excel'; 
                                        ?> file-icon text-primary"></i>
                                        <h4 class="mt-3">مستند Office</h4>
                                        <p class="text-muted">انقر على "تحميل الملف" لفتح المستند في التطبيق المناسب</p>
                                        <a href="../../<?php echo $document['file_path']; ?>" 
                                           class="btn btn-primary" target="_blank">
                                            <i class="bi bi-download me-2"></i>
                                            تحميل وفتح الملف
                                        </a>
                                    </div>
                                    
                                <?php else: ?>
                                    <!-- أنواع ملفات أخرى -->
                                    <div class="py-5">
                                        <i class="bi bi-file-earmark file-icon"></i>
                                        <h4 class="mt-3">ملف <?php echo strtoupper($fileExtension); ?></h4>
                                        <p class="text-muted">انقر على "تحميل الملف" لتحميل المستند</p>
                                        <a href="../../<?php echo $document['file_path']; ?>" 
                                           class="btn btn-primary" target="_blank">
                                            <i class="bi bi-download me-2"></i>
                                            تحميل الملف
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../../assets/js/main.js"></script>
    
    <script>
        function printDocument() {
            // فتح نافذة طباعة للمستند
            const printWindow = window.open('../../<?php echo $document['file_path']; ?>', '_blank');
            printWindow.onload = function() {
                printWindow.print();
            };
        }
        
        // إخفاء الرسائل تلقائياً
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                alert.style.transition = 'opacity 0.5s ease';
                alert.style.opacity = '0';
                setTimeout(function() {
                    alert.remove();
                }, 500);
            });
        }, 5000);
    </script>
</body>
</html>

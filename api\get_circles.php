<?php
/**
 * API للحصول على الدوائر حسب الإدارة
 * Get Circles by Department API
 */

require_once '../config/database.php';
require_once '../includes/auth.php';

// التحقق من تسجيل الدخول
requireLogin();

header('Content-Type: application/json; charset=utf-8');

try {
    $departmentId = (int)($_GET['department_id'] ?? 0);
    
    if ($departmentId <= 0) {
        sendJsonResponse(['error' => 'معرف الإدارة مطلوب'], 400);
    }
    
    $db = getDB();
    $stmt = $db->prepare("
        SELECT id, circle_name 
        FROM circles 
        WHERE department_id = ? AND is_active = 1 
        ORDER BY circle_order
    ");
    $stmt->execute([$departmentId]);
    $circles = $stmt->fetchAll();
    
    sendJsonResponse($circles);
    
} catch (Exception $e) {
    logError("خطأ في جلب الدوائر: " . $e->getMessage());
    sendJsonResponse(['error' => 'خطأ في الخادم'], 500);
}
?>

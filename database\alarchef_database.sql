-- قاعدة بيانات نظام أرشفة مصنع أسمنت البرح
-- ALarchef Archive System Database

CREATE DATABASE IF NOT EXISTS alarchef_archive CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE alarchef_archive;

-- جد<PERSON><PERSON> المستخدمين
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    email VARCHAR(100),
    role ENUM('admin', 'archive_staff', 'medical_staff', 'employee') DEFAULT 'employee',
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول نوع العمالة
CREATE TABLE employment_types (
    id INT AUTO_INCREMENT PRIMARY KEY,
    type_name VARCHAR(50) NOT NULL,
    type_order INT NOT NULL,
    is_active TINYINT(1) DEFAULT 1
);

-- إدراج بيانات نوع العمالة
INSERT INTO employment_types (type_name, type_order) VALUES
('رسمي', 1),
('متقاعد', 2),
('متعاقد', 3),
('اجر يومي', 4),
('منقول', 5);

-- جدول الإدارات
CREATE TABLE departments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    dept_name VARCHAR(100) NOT NULL,
    dept_number INT NOT NULL,
    is_active TINYINT(1) DEFAULT 1
);

-- إدراج بيانات الإدارات
INSERT INTO departments (dept_name, dept_number) VALUES
('ادارة عليا', 1),
('اداره الانتاج', 2),
('اداره الفنيه', 3),
('الشئون الاداريه', 4),
('اداره الخدمات والمشاريع', 5),
('اداره الماليه', 6),
('اداره المراجعه والتفتيش', 7),
('اداره المبيعات', 8),
('ادارة المصنع', 9);

-- جدول الدوائر
CREATE TABLE circles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    circle_name VARCHAR(100) NOT NULL,
    circle_order INT NOT NULL,
    department_id INT NOT NULL,
    is_active TINYINT(1) DEFAULT 1,
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE CASCADE
);

-- إدراج بيانات الدوائر
INSERT INTO circles (circle_name, circle_order, department_id) VALUES
('دائره المحاجر', 1, 2),
('دائره التشغيل', 2, 2),
('دائره الصيانه الميكانيكية', 1, 3),
('دائره الصيانه الكهربائية', 2, 3),
('شئون الموظفين', 1, 4),
('دائره الرعاية الطبية والتأمين', 2, 4),
('دائره الخدمات العامه', 1, 5),
('دائره الإسكان والمشاريع', 2, 5),
('دائره الحسابات', 1, 6),
('دائره المشتريات والمخازن', 2, 6),
('دائره التكاليف', 3, 6),
('دائره المراجعه', 1, 7),
('دائره الرقابة والتفتيش', 2, 7),
('دائره المبيعات والترحيل', 1, 8),
('دائره التسويق والمعارض', 2, 8),
('مكتب المدير العام', 1, 9),
('دائرة المختبر', 2, 9),
('دائره التأكيد والجوده', 3, 9),
('العلاقات العامه', 4, 9),
('دائره الحاسوب والمعلومات', 5, 9),
('دائره الامن الصناعي', 6, 9),
('دائره التخطيط والدرسات', 7, 9),
('دائره الشئون القانونية', 8, 9),
('دائره التدريب والتأهيل', 9, 9),
('مكتب عدن', 10, 9);

-- جدول الأقسام
CREATE TABLE sections (
    id INT AUTO_INCREMENT PRIMARY KEY,
    section_name VARCHAR(100) NOT NULL,
    section_order INT NOT NULL,
    circle_id INT NOT NULL,
    is_active TINYINT(1) DEFAULT 1,
    FOREIGN KEY (circle_id) REFERENCES circles(id) ON DELETE CASCADE
);

-- جدول المجموعات الوظيفية
CREATE TABLE job_groups (
    id INT AUTO_INCREMENT PRIMARY KEY,
    group_name VARCHAR(50) NOT NULL,
    group_number INT NOT NULL,
    is_active TINYINT(1) DEFAULT 1
);

-- إدراج بيانات المجموعات الوظيفية
INSERT INTO job_groups (group_name, group_number) VALUES
('ادارة عليا', 1),
('تخصصية', 2),
('اشرافية', 3),
('كتابية', 4),
('فنية', 5),
('حرفية', 6),
('عمالية', 7);

-- جدول المسميات الوظيفية
CREATE TABLE job_titles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title_name VARCHAR(100) NOT NULL,
    title_order INT NOT NULL,
    job_group_id INT NOT NULL,
    is_active TINYINT(1) DEFAULT 1,
    FOREIGN KEY (job_group_id) REFERENCES job_groups(id) ON DELETE CASCADE
);

-- إدراج بيانات المسميات الوظيفية
INSERT INTO job_titles (title_name, title_order, job_group_id) VALUES
('وكيل وزارة', 1, 1),
('مساعد وكيل وزاره', 2, 1),
('كبير اختصاصين', 1, 2),
('اختصاصى', 2, 2),
('مساعد اخصائي', 3, 2),
('اختصاصى مساعد', 4, 2),
('مدير اداره', 1, 3),
('نائب مدير اداره', 2, 3),
('مدير دائره', 3, 3),
('رئيس قسم', 4, 3),
('رئيس وحده', 5, 3),
('مشرف قسم', 6, 3),
('مشرف', 7, 3),
('امين مخزن', 8, 3),
('امين مخزن المهمات', 9, 3),
('رئيس كتبه', 1, 4),
('كاتب', 2, 4),
('كاتب مساعد', 3, 4),
('رئيس فنيين', 1, 5),
('فني', 2, 5),
('فني مساعد', 3, 5),
('رئيس حرفيين', 1, 6),
('حرفي', 2, 6),
('حرفي مساعد', 3, 6),
('سائق', 4, 6),
('حرفي', 5, 6),
('ملاحظ', 1, 7),
('عامل', 2, 7),
('عامل نظافة', 3, 7);

-- جدول البيانات الشخصية للموظفين
CREATE TABLE employees_personal (
    id INT AUTO_INCREMENT PRIMARY KEY,
    employee_number VARCHAR(20) NOT NULL UNIQUE,
    full_name VARCHAR(100) NOT NULL,
    birth_date DATE NOT NULL,
    national_id VARCHAR(11) NOT NULL UNIQUE,
    blood_type VARCHAR(5),
    birth_place VARCHAR(100),
    residence_address TEXT,
    phone_number VARCHAR(20),
    gender ENUM('ذكر', 'انثى') NOT NULL,
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول البيانات الوظيفية للموظفين
CREATE TABLE employees_job (
    id INT AUTO_INCREMENT PRIMARY KEY,
    employee_id INT NOT NULL,
    job_number VARCHAR(7) NOT NULL UNIQUE,
    appointment_date DATE NOT NULL,
    employment_type_id INT NOT NULL,
    department_id INT NOT NULL,
    circle_id INT,
    section_id INT,
    job_title_id INT,
    job_position VARCHAR(100),
    level_number INT CHECK (level_number BETWEEN 0 AND 6),
    grade_number INT CHECK (grade_number BETWEEN 1 AND 20),
    workplace VARCHAR(100),
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (employee_id) REFERENCES employees_personal(id) ON DELETE CASCADE,
    FOREIGN KEY (employment_type_id) REFERENCES employment_types(id),
    FOREIGN KEY (department_id) REFERENCES departments(id),
    FOREIGN KEY (circle_id) REFERENCES circles(id),
    FOREIGN KEY (section_id) REFERENCES sections(id),
    FOREIGN KEY (job_title_id) REFERENCES job_titles(id)
);

-- جدول أنواع المستندات
CREATE TABLE document_types (
    id INT AUTO_INCREMENT PRIMARY KEY,
    type_name VARCHAR(100) NOT NULL,
    is_active TINYINT(1) DEFAULT 1
);

-- إدراج أنواع المستندات
INSERT INTO document_types (type_name) VALUES
('قرار التعيين'),
('العقود'),
('المؤهلات العلمية'),
('التسويات الوظيفية'),
('شهادات الخبره'),
('القرارات الإدارية'),
('الاعاره'),
('النقل'),
('الانتداب'),
('الترقيات'),
('الجزاءات'),
('الإجازات'),
('إنهاء الخدمة');

-- جدول المستندات
CREATE TABLE documents (
    id INT AUTO_INCREMENT PRIMARY KEY,
    employee_id INT NOT NULL,
    document_type_id INT NOT NULL,
    document_title VARCHAR(200) NOT NULL,
    document_description TEXT,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT,
    file_type VARCHAR(10),
    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    uploaded_by INT NOT NULL,
    is_active TINYINT(1) DEFAULT 1,
    FOREIGN KEY (employee_id) REFERENCES employees_personal(id) ON DELETE CASCADE,
    FOREIGN KEY (document_type_id) REFERENCES document_types(id),
    FOREIGN KEY (uploaded_by) REFERENCES users(id)
);

-- جدول أنواع الحركات الوظيفية
CREATE TABLE job_movement_types (
    id INT AUTO_INCREMENT PRIMARY KEY,
    type_name VARCHAR(100) NOT NULL,
    is_group_action TINYINT(1) DEFAULT 0,
    is_active TINYINT(1) DEFAULT 1
);

-- إدراج أنواع الحركات الوظيفية
INSERT INTO job_movement_types (type_name, is_group_action) VALUES
('قرار توظيف او تعيين جماعي', 1),
('تسويات بالخدمة جماعي', 1),
('تسويات بالمؤهل جماعي', 1),
('نقل', 1),
('اعاره', 1),
('اومر إدارية جماعية', 1),
('ترقية فردية', 0),
('نقل فردي', 0),
('اعارة فردية', 0),
('تسوية فردية', 0),
('امر اداري فردي', 0);

-- جدول الحركات الوظيفية
CREATE TABLE job_movements (
    id INT AUTO_INCREMENT PRIMARY KEY,
    movement_type_id INT NOT NULL,
    movement_title VARCHAR(200) NOT NULL,
    movement_description TEXT,
    movement_date DATE NOT NULL,
    is_group_movement TINYINT(1) DEFAULT 0,
    document_file VARCHAR(500),
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active TINYINT(1) DEFAULT 1,
    FOREIGN KEY (movement_type_id) REFERENCES job_movement_types(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- جدول تفاصيل الحركات الوظيفية
CREATE TABLE job_movement_details (
    id INT AUTO_INCREMENT PRIMARY KEY,
    movement_id INT NOT NULL,
    employee_id INT NOT NULL,
    from_department_id INT,
    to_department_id INT,
    from_circle_id INT,
    to_circle_id INT,
    from_section_id INT,
    to_section_id INT,
    from_job_title_id INT,
    to_job_title_id INT,
    from_grade INT,
    to_grade INT,
    from_level INT,
    to_level INT,
    settlement_amount DECIMAL(10,2),
    external_entity VARCHAR(200),
    notes TEXT,
    FOREIGN KEY (movement_id) REFERENCES job_movements(id) ON DELETE CASCADE,
    FOREIGN KEY (employee_id) REFERENCES employees_personal(id),
    FOREIGN KEY (from_department_id) REFERENCES departments(id),
    FOREIGN KEY (to_department_id) REFERENCES departments(id),
    FOREIGN KEY (from_circle_id) REFERENCES circles(id),
    FOREIGN KEY (to_circle_id) REFERENCES circles(id),
    FOREIGN KEY (from_section_id) REFERENCES sections(id),
    FOREIGN KEY (to_section_id) REFERENCES sections(id),
    FOREIGN KEY (from_job_title_id) REFERENCES job_titles(id),
    FOREIGN KEY (to_job_title_id) REFERENCES job_titles(id)
);

-- جدول المراسلات
CREATE TABLE correspondences (
    id INT AUTO_INCREMENT PRIMARY KEY,
    correspondence_number VARCHAR(50) NOT NULL,
    correspondence_type ENUM('صادرة', 'واردة') NOT NULL,
    subject VARCHAR(200) NOT NULL,
    from_entity VARCHAR(200),
    to_entity VARCHAR(200),
    correspondence_date DATE NOT NULL,
    received_date DATE,
    classification VARCHAR(100),
    priority ENUM('عادي', 'مهم', 'عاجل', 'سري') DEFAULT 'عادي',
    content TEXT,
    file_attachment VARCHAR(500),
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active TINYINT(1) DEFAULT 1,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- جدول محاضر الاجتماعات
CREATE TABLE meeting_minutes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    meeting_title VARCHAR(200) NOT NULL,
    meeting_type VARCHAR(100),
    meeting_date DATE NOT NULL,
    meeting_time TIME,
    meeting_location VARCHAR(200),
    attendees TEXT,
    agenda TEXT,
    decisions TEXT,
    next_meeting_date DATE,
    file_attachment VARCHAR(500),
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active TINYINT(1) DEFAULT 1,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- جدول الزيارات الطبية
CREATE TABLE medical_visits (
    id INT AUTO_INCREMENT PRIMARY KEY,
    visit_number VARCHAR(50) NOT NULL,
    employee_id INT NOT NULL,
    beneficiary_name VARCHAR(100),
    beneficiary_relation ENUM('الموظف نفسه', 'من يعول') DEFAULT 'الموظف نفسه',
    visit_entity VARCHAR(200),
    visit_date DATE NOT NULL,
    visit_reason TEXT,
    diagnosis TEXT,
    treatment_prescribed TEXT,
    cost DECIMAL(10,2),
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active TINYINT(1) DEFAULT 1,
    FOREIGN KEY (employee_id) REFERENCES employees_personal(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- جدول التقارير الطبية
CREATE TABLE medical_reports (
    id INT AUTO_INCREMENT PRIMARY KEY,
    employee_id INT NOT NULL,
    report_title VARCHAR(200) NOT NULL,
    medical_condition TEXT,
    medical_decision TEXT,
    issuing_entity VARCHAR(200),
    report_date DATE NOT NULL,
    file_attachment VARCHAR(500),
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active TINYINT(1) DEFAULT 1,
    FOREIGN KEY (employee_id) REFERENCES employees_personal(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- جدول العلاجات والأدوية
CREATE TABLE treatments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    employee_id INT NOT NULL,
    treatment_type ENUM('علاج عادي', 'علاج مزمن', 'نظارات') NOT NULL,
    annual_balance DECIMAL(10,2),
    used_amount DECIMAL(10,2) DEFAULT 0,
    remaining_balance DECIMAL(10,2),
    year YEAR NOT NULL,
    notes TEXT,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active TINYINT(1) DEFAULT 1,
    FOREIGN KEY (employee_id) REFERENCES employees_personal(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- جدول وثائق التأمين
CREATE TABLE insurance_documents (
    id INT AUTO_INCREMENT PRIMARY KEY,
    employee_id INT NOT NULL,
    insurance_type ENUM('تأمين الحياة', 'تأمين السيارات', 'تأمين طبي') NOT NULL,
    policy_number VARCHAR(100),
    insurance_company VARCHAR(200),
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    coverage_amount DECIMAL(12,2),
    premium_amount DECIMAL(10,2),
    file_attachment VARCHAR(500),
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active TINYINT(1) DEFAULT 1,
    FOREIGN KEY (employee_id) REFERENCES employees_personal(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- جدول إعدادات النظام
CREATE TABLE system_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    setting_description VARCHAR(200),
    updated_by INT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (updated_by) REFERENCES users(id)
);

-- إدراج الإعدادات الافتراضية
INSERT INTO system_settings (setting_key, setting_value, setting_description) VALUES
('system_name', 'نظام أرشفة مصنع أسمنت البرح', 'اسم النظام'),
('session_timeout', '0', 'مهلة الجلسة بالثواني (0 = بلا حدود)'),
('timezone', 'Asia/Aden', 'المنطقة الزمنية'),
('max_file_size', '10485760', 'الحد الأقصى لحجم الملف بالبايت (10MB)'),
('allowed_file_types', 'pdf,jpg,jpeg,png,doc,docx,xls,xlsx,csv', 'أنواع الملفات المسموحة'),
('backup_path', 'backups/', 'مسار النسخ الاحتياطية'),
('retirement_age', '60', 'سن التقاعد'),
('service_years', '35', 'سنوات الخدمة للتقاعد');

-- إدراج بيانات الأقسام
INSERT INTO sections (section_name, section_order, circle_id) VALUES
('سكرتارية المدير العام', 1, 16),
('سكرتارية نائب المدير العام', 2, 16),
('التعدين', 1, 1),
('الكسارات', 2, 1),
('الخدمات', 1, 7),
('الحركه', 2, 7),
('الوحده الصحيه', 3, 7),
('ورشة الصيانة الميكانيكية', 1, 3),
('الاليات', 2, 3),
('الوحدات المساعدة', 3, 3),
('صيانة التغليف', 4, 3),
('صيانة الطواحين', 5, 3),
('صيانة الفرن', 6, 3),
('صيانة الكسارات والسيور', 7, 3),
('السجلات المحاسبية', 1, 9),
('الإيرادات', 2, 9),
('المبيعات', 1, 14),
('الترحيل', 2, 14),
('المراجعه', 1, 12),
('السجلات والارشف', 1, 5),
('الاستحقاقات', 2, 5),
('الرقابة والدوام', 3, 5),
('السكرتاريه العامه والمحفوظات', 4, 5),
('الحرس', 5, 5),
('طاحونة الخام', 1, 2),
('الفرن', 2, 2),
('طاحونة الاسمنت', 3, 2),
('التغليف', 4, 2),
('المشاريع', 1, 8),
('الصيانه الكهربائية', 1, 4),
('الاجهزه الدقيقه', 2, 4),
('الالكترونيات', 3, 4),
('الشبكات والورديات', 4, 4),
('كهرباء التغليف', 5, 4),
('المشتريات الخارجية', 1, 10),
('المشتريات الداخليه', 2, 10),
('المخازن', 3, 10),
('التسويق والرقابه', 1, 15),
('الرقابة والتفتيش', 1, 13),
('الرعايه الطبية', 1, 6),
('التأمينات', 2, 6),
('الاشعه', 1, 17),
('العينات', 2, 17),
('الفيزياء', 3, 17),
('الكيمياء', 4, 17),
('تأكيد الجودة', 1, 18),
('رقابه المخزون', 1, 11),
('التكاليف الفعلية', 2, 11),
('التكاليف المعيارية', 3, 11),
('العلاقات العامه', 1, 19),
('التوثيق والمعلومات', 1, 20),
('النظم والصيانه', 2, 20),
('السلامه المهنيه', 1, 21),
('الاطفاء', 2, 21),
('البئية', 3, 21),
('التخطيط', 1, 22),
('الإحصاء', 2, 22),
('التحقيقات', 1, 23),
('الدراسات والعقود', 2, 23),
('التدريب', 1, 24),
('التأهيل', 2, 24),
('مكتب عدن', 1, 25);

-- إنشاء مستخدم افتراضي (admin/admin123)
INSERT INTO users (username, password, full_name, email, role) VALUES
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير النظام', '<EMAIL>', 'admin');

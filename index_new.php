<?php
/**
 * الصفحة الرئيسية المبسطة
 * Simplified Main Page
 */

// بدء الجلسة
session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: login_new.php');
    exit;
}

// إعدادات قاعدة البيانات
$host = 'localhost';
$dbname = 'alarchef_archive';
$username = 'root';
$password = '';

// جلب الإحصائيات
$totalEmployees = 0;
$totalDocuments = 0;
$totalCorrespondences = 0;
$totalMedicalVisits = 0;

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // إحصائيات الموظفين
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM employees_personal WHERE is_active = 1");
    $result = $stmt->fetch();
    $totalEmployees = $result ? $result['total'] : 0;
    
    // إحصائيات المستندات
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM documents WHERE is_active = 1");
    $result = $stmt->fetch();
    $totalDocuments = $result ? $result['total'] : 0;
    
    // إحصائيات المراسلات
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM correspondences WHERE is_active = 1");
    $result = $stmt->fetch();
    $totalCorrespondences = $result ? $result['total'] : 0;
    
    // إحصائيات الزيارات الطبية
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM medical_visits WHERE is_active = 1");
    $result = $stmt->fetch();
    $totalMedicalVisits = $result ? $result['total'] : 0;
    
} catch (PDOException $e) {
    // في حالة الخطأ، استخدم القيم الافتراضية
}

// معالجة تسجيل الخروج
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: login_new.php');
    exit;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام أرشفة مصنع أسمنت البرح</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 4px rgba(0,0,0,.1);
        }
        
        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
            border: none;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin-bottom: 1rem;
        }
        
        .stat-primary { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .stat-success { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        .stat-info { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
        .stat-warning { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #333;
            margin: 0;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 1.1rem;
            margin: 0;
        }
        
        .welcome-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .quick-action-btn {
            border-radius: 10px;
            padding: 1rem;
            text-align: center;
            transition: all 0.3s ease;
            text-decoration: none;
            display: block;
            height: 100px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        
        .quick-action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bi bi-building me-2"></i>
                نظام أرشفة مصنع أسمنت البرح
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="bi bi-person-circle me-1"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="?logout=1">
                            <i class="bi bi-box-arrow-right me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <!-- Welcome Card -->
        <div class="welcome-card">
            <h1 class="mb-3">
                <i class="bi bi-speedometer2 me-2"></i>
                مرحباً بك في لوحة التحكم
            </h1>
            <p class="mb-0">
                أهلاً وسهلاً <?php echo htmlspecialchars($_SESSION['full_name']); ?>، 
                اليوم <?php echo date('Y-m-d'); ?>
            </p>
        </div>
        
        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="stat-card">
                    <div class="stat-icon stat-primary mx-auto">
                        <i class="bi bi-people-fill"></i>
                    </div>
                    <h3 class="stat-number text-center"><?php echo $totalEmployees; ?></h3>
                    <p class="stat-label text-center">إجمالي الموظفين</p>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="stat-card">
                    <div class="stat-icon stat-success mx-auto">
                        <i class="bi bi-file-earmark-text-fill"></i>
                    </div>
                    <h3 class="stat-number text-center"><?php echo $totalDocuments; ?></h3>
                    <p class="stat-label text-center">المستندات المؤرشفة</p>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="stat-card">
                    <div class="stat-icon stat-info mx-auto">
                        <i class="bi bi-envelope-fill"></i>
                    </div>
                    <h3 class="stat-number text-center"><?php echo $totalCorrespondences; ?></h3>
                    <p class="stat-label text-center">المراسلات</p>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="stat-card">
                    <div class="stat-icon stat-warning mx-auto">
                        <i class="bi bi-heart-pulse-fill"></i>
                    </div>
                    <h3 class="stat-number text-center"><?php echo $totalMedicalVisits; ?></h3>
                    <p class="stat-label text-center">الزيارات الطبية</p>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-lightning-charge me-2"></i>
                            الإجراءات السريعة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-lg-3 col-md-6 mb-3">
                                <a href="pages/employees/add.php" class="btn btn-outline-primary quick-action-btn">
                                    <i class="bi bi-person-plus-fill mb-2" style="font-size: 2rem;"></i>
                                    إضافة موظف جديد
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-6 mb-3">
                                <a href="pages/documents/upload.php" class="btn btn-outline-success quick-action-btn">
                                    <i class="bi bi-cloud-upload-fill mb-2" style="font-size: 2rem;"></i>
                                    رفع مستند
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-6 mb-3">
                                <a href="pages/correspondences/add.php" class="btn btn-outline-info quick-action-btn">
                                    <i class="bi bi-envelope-plus-fill mb-2" style="font-size: 2rem;"></i>
                                    إضافة مراسلة
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-6 mb-3">
                                <a href="pages/medical/add_visit.php" class="btn btn-outline-warning quick-action-btn">
                                    <i class="bi bi-hospital-fill mb-2" style="font-size: 2rem;"></i>
                                    تسجيل زيارة طبية
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- System Status -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-info-circle me-2"></i>
                            حالة النظام
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-success">
                            <i class="bi bi-check-circle-fill me-2"></i>
                            النظام يعمل بشكل طبيعي
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <h6>معلومات المستخدم:</h6>
                                <ul class="list-unstyled">
                                    <li><strong>الاسم:</strong> <?php echo htmlspecialchars($_SESSION['full_name']); ?></li>
                                    <li><strong>اسم المستخدم:</strong> <?php echo htmlspecialchars($_SESSION['username']); ?></li>
                                    <li><strong>الدور:</strong> <?php echo htmlspecialchars($_SESSION['role']); ?></li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>معلومات النظام:</h6>
                                <ul class="list-unstyled">
                                    <li><strong>إصدار PHP:</strong> <?php echo phpversion(); ?></li>
                                    <li><strong>التاريخ:</strong> <?php echo date('Y-m-d H:i:s'); ?></li>
                                    <li><strong>المنطقة الزمنية:</strong> <?php echo date_default_timezone_get(); ?></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

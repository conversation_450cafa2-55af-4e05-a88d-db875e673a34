/**
 * ملف JavaScript الرئيسي - نظام أرشفة مصنع أسمنت البرح
 * Main JavaScript File - ALarchef Archive System
 */

// إعدادات عامة
const CONFIG = {
    baseUrl: window.location.origin + '/ALarchef2/',
    apiUrl: window.location.origin + '/ALarchef2/api/',
    maxFileSize: 10485760, // 10MB
    allowedFileTypes: ['pdf', 'jpg', 'jpeg', 'png', 'doc', 'docx', 'xls', 'xlsx', 'csv']
};

// دالة لإظهار الرسائل
function showMessage(message, type = 'info', duration = 5000) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; left: 20px; z-index: 9999; min-width: 300px;';
    
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // إزالة الرسالة تلقائياً
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, duration);
}

// دالة لإظهار مؤشر التحميل
function showLoading(element, text = 'جاري التحميل...') {
    const originalContent = element.innerHTML;
    element.innerHTML = `
        <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
        ${text}
    `;
    element.disabled = true;
    
    return () => {
        element.innerHTML = originalContent;
        element.disabled = false;
    };
}

// دالة للتحقق من صحة الملف
function validateFile(file) {
    const errors = [];
    
    // التحقق من حجم الملف
    if (file.size > CONFIG.maxFileSize) {
        errors.push(`حجم الملف كبير جداً. الحد الأقصى ${formatFileSize(CONFIG.maxFileSize)}`);
    }
    
    // التحقق من نوع الملف
    const fileExtension = file.name.split('.').pop().toLowerCase();
    if (!CONFIG.allowedFileTypes.includes(fileExtension)) {
        errors.push(`نوع الملف غير مسموح. الأنواع المسموحة: ${CONFIG.allowedFileTypes.join(', ')}`);
    }
    
    return {
        isValid: errors.length === 0,
        errors: errors
    };
}

// دالة لتنسيق حجم الملف
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// دالة لرفع الملفات
function uploadFile(file, uploadUrl, progressCallback) {
    return new Promise((resolve, reject) => {
        const formData = new FormData();
        formData.append('file', file);
        
        const xhr = new XMLHttpRequest();
        
        // تتبع التقدم
        xhr.upload.addEventListener('progress', (e) => {
            if (e.lengthComputable && progressCallback) {
                const percentComplete = (e.loaded / e.total) * 100;
                progressCallback(percentComplete);
            }
        });
        
        xhr.addEventListener('load', () => {
            if (xhr.status === 200) {
                try {
                    const response = JSON.parse(xhr.responseText);
                    resolve(response);
                } catch (e) {
                    reject(new Error('خطأ في تحليل الاستجابة'));
                }
            } else {
                reject(new Error(`خطأ في الرفع: ${xhr.status}`));
            }
        });
        
        xhr.addEventListener('error', () => {
            reject(new Error('خطأ في الشبكة'));
        });
        
        xhr.open('POST', uploadUrl);
        xhr.send(formData);
    });
}

// دالة لإرسال طلبات AJAX
function sendRequest(url, method = 'GET', data = null, headers = {}) {
    return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        
        xhr.addEventListener('load', () => {
            if (xhr.status >= 200 && xhr.status < 300) {
                try {
                    const response = JSON.parse(xhr.responseText);
                    resolve(response);
                } catch (e) {
                    resolve(xhr.responseText);
                }
            } else {
                reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));
            }
        });
        
        xhr.addEventListener('error', () => {
            reject(new Error('خطأ في الشبكة'));
        });
        
        xhr.open(method, url);
        
        // إضافة الرؤوس
        Object.keys(headers).forEach(key => {
            xhr.setRequestHeader(key, headers[key]);
        });
        
        if (data && method !== 'GET') {
            if (data instanceof FormData) {
                xhr.send(data);
            } else {
                xhr.setRequestHeader('Content-Type', 'application/json');
                xhr.send(JSON.stringify(data));
            }
        } else {
            xhr.send();
        }
    });
}

// دالة لتأكيد الحذف
function confirmDelete(message = 'هل أنت متأكد من الحذف؟') {
    return new Promise((resolve) => {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">تأكيد الحذف</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p>${message}</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-danger" id="confirmDeleteBtn">حذف</button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        const bootstrapModal = new bootstrap.Modal(modal);
        bootstrapModal.show();
        
        modal.querySelector('#confirmDeleteBtn').addEventListener('click', () => {
            bootstrapModal.hide();
            resolve(true);
        });
        
        modal.addEventListener('hidden.bs.modal', () => {
            modal.remove();
            resolve(false);
        });
    });
}

// دالة لتحويل التاريخ إلى التنسيق العربي
function formatArabicDate(dateString) {
    if (!dateString) return '';
    
    const date = new Date(dateString);
    const arabicMonths = [
        'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
        'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    
    const day = date.getDate();
    const month = arabicMonths[date.getMonth()];
    const year = date.getFullYear();
    
    return `${day} ${month} ${year}`;
}

// دالة لتحويل الأرقام إلى العربية
function convertToArabicNumbers(str) {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return str.toString().replace(/[0-9]/g, (w) => arabicNumbers[+w]);
}

// دالة لتصدير الجدول إلى Excel
function exportTableToExcel(tableId, filename = 'data.xlsx') {
    const table = document.getElementById(tableId);
    if (!table) {
        showMessage('الجدول غير موجود', 'error');
        return;
    }
    
    // إنشاء workbook جديد
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.table_to_sheet(table);
    
    XLSX.utils.book_append_sheet(wb, ws, 'البيانات');
    XLSX.writeFile(wb, filename);
}

// دالة لطباعة الصفحة
function printPage() {
    window.print();
}

// دالة لإنشاء QR Code
function generateQRCode(text, elementId) {
    const element = document.getElementById(elementId);
    if (element && typeof QRCode !== 'undefined') {
        element.innerHTML = '';
        new QRCode(element, {
            text: text,
            width: 128,
            height: 128,
            colorDark: '#000000',
            colorLight: '#ffffff'
        });
    }
}

// دالة لحفظ البيانات في localStorage
function saveToLocalStorage(key, data) {
    try {
        localStorage.setItem(key, JSON.stringify(data));
        return true;
    } catch (e) {
        console.error('خطأ في حفظ البيانات:', e);
        return false;
    }
}

// دالة لجلب البيانات من localStorage
function getFromLocalStorage(key) {
    try {
        const data = localStorage.getItem(key);
        return data ? JSON.parse(data) : null;
    } catch (e) {
        console.error('خطأ في جلب البيانات:', e);
        return null;
    }
}

// دالة لتهيئة DataTable
function initDataTable(tableId, options = {}) {
    const defaultOptions = {
        language: {
            url: 'https://cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json'
        },
        responsive: true,
        pageLength: 25,
        lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, 'الكل']],
        dom: 'Bfrtip',
        buttons: [
            {
                extend: 'excel',
                text: 'تصدير Excel',
                className: 'btn btn-success'
            },
            {
                extend: 'pdf',
                text: 'تصدير PDF',
                className: 'btn btn-danger'
            },
            {
                extend: 'print',
                text: 'طباعة',
                className: 'btn btn-info'
            }
        ]
    };
    
    const finalOptions = { ...defaultOptions, ...options };
    
    if ($.fn.DataTable.isDataTable(`#${tableId}`)) {
        $(`#${tableId}`).DataTable().destroy();
    }
    
    return $(`#${tableId}`).DataTable(finalOptions);
}

// تهيئة الأحداث عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // تهيئة popovers
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // تهيئة النماذج
    const forms = document.querySelectorAll('.needs-validation');
    forms.forEach(form => {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    });
    
    // تهيئة رفع الملفات
    const fileInputs = document.querySelectorAll('input[type="file"]');
    fileInputs.forEach(input => {
        input.addEventListener('change', function() {
            const files = Array.from(this.files);
            files.forEach(file => {
                const validation = validateFile(file);
                if (!validation.isValid) {
                    showMessage(validation.errors.join('<br>'), 'error');
                    this.value = '';
                }
            });
        });
    });
    
    // تهيئة أزرار الحذف
    const deleteButtons = document.querySelectorAll('.btn-delete');
    deleteButtons.forEach(button => {
        button.addEventListener('click', async function(e) {
            e.preventDefault();
            const confirmed = await confirmDelete();
            if (confirmed) {
                // تنفيذ عملية الحذف
                const url = this.getAttribute('href') || this.dataset.url;
                if (url) {
                    window.location.href = url;
                }
            }
        });
    });
    
    // تحديث الوقت كل دقيقة
    setInterval(() => {
        const timeElements = document.querySelectorAll('.current-time');
        timeElements.forEach(element => {
            element.textContent = new Date().toLocaleString('ar-YE');
        });
    }, 60000);
});

// تصدير الدوال للاستخدام العام
window.ALarchef = {
    showMessage,
    showLoading,
    validateFile,
    formatFileSize,
    uploadFile,
    sendRequest,
    confirmDelete,
    formatArabicDate,
    convertToArabicNumbers,
    exportTableToExcel,
    printPage,
    generateQRCode,
    saveToLocalStorage,
    getFromLocalStorage,
    initDataTable,
    CONFIG
};
